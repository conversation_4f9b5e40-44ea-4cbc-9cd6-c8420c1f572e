import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Printer, ArrowUp, ArrowDown, Trash2 } from 'lucide-react';
import { fetchCaixaOperations, createCaixaOperation, deleteCaixaOperation, CaixaOperation } from '../lib/caixaApi';
import { fetchCaixaCount, saveCaixaCount, closeCaixaDay } from '../lib/caixaCountApi';

interface ReceiptProps {
  operation: CaixaOperation;
  onClose: () => void;
}

function OperationReceipt({ operation, onClose }: ReceiptProps) {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      window.print();
    }, 100);
    return () => clearTimeout(timeoutId);
  }, []);

  const formatCurrency = (value: number) => {
    return `R$ ${(Math.round(value * 100) / 100).toFixed(2).replace('.', ',')}`;
  };
  const operationTypeText = operation.type === 'suprimento' ? 'SUPRIMENTO DE CAIXA' : 'SANGRIA DE CAIXA';

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-4">
          <div className="receipt">
            <style>{`
              @media print {
                body * { visibility: hidden; }
                .receipt, .receipt * { visibility: visible; }
                .receipt {
                  position: absolute; left: 0; top: 0; width: 80mm; padding: 5mm;
                  background: white !important;
                }
                .no-print { display: none; }
                .receipt * {
                  color: black !important;
                  font-weight: bold !important;
                  text-shadow: none !important;
                }
                .receipt img { filter: contrast(200%) brightness(0) !important; }
                .receipt-divider {
                  margin: 0.5em 0 !important;
                  border-top: 1px solid black !important;
                }
                .signature-line {
                  margin-top: 2em !important;
                  border-top: 1px solid black !important;
                  padding-top: 0.5em !important;
                }
              }
              .receipt {
                font-family: monospace;
                width: 80mm;
                margin: 0 auto;
                padding: 5mm;
                text-align: center;
              }
              .receipt-header {
                text-align: center;
                margin-bottom: 0.5em;
              }
              .receipt-logo {
                height: 32px;
                width: auto;
                margin: 0 auto 0.5em;
                display: block;
                filter: contrast(200%) brightness(0);
              }
              .receipt-divider {
                border-top: 1px solid black;
                margin: 0.5em 0;
              }
              .receipt p {
                margin: 0.3em 0;
              }
              .signature-line {
                margin-top: 2em;
                border-top: 1px solid black;
                padding-top: 0.5em;
                text-align: center;
              }
            `}</style>
            <div className="receipt-header">
              <img src="/recibo.png" alt="Delivery CRM" className="receipt-logo" />
              <div>
                <strong>{operationTypeText}</strong>
                <br />
                {format(new Date(operation.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
              </div>
            </div>
            <div className="receipt-divider" />
            <div style={{ textAlign: 'center', margin: '1em 0' }}>
              <p><strong>Valor:</strong> {formatCurrency(operation.amount)}</p>
              <p><strong>Descrição:</strong> {operation.description}</p>
            </div>
            <div className="receipt-divider" />
            <div className="signature-line">Assinatura</div>
          </div>
          <div className="mt-4 flex justify-end no-print">
            <button onClick={onClose} className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">Fechar</button>
          </div>
        </div>
      </div>
    </div>
  );
}

type Tab = 'operations' | 'count';

export default function CaixaManagement() {
  const [operations, setOperations] = useState<CaixaOperation[]>([]);
  const [loading, setLoading] = useState(true);
  const [type, setType] = useState<'suprimento'|'sangria'>('suprimento');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [printOp, setPrintOp] = useState<CaixaOperation|null>(null);
  const [error, setError] = useState<string|null>(null);
  const [activeTab, setActiveTab] = useState<Tab>('operations');
  const [cashNotes, setCashNotes] = useState<{[k:string]:number}>({
    '200': 0, '100': 0, '50': 0, '20': 0, '10': 0, '5': 0, '2': 0
  });
  const [cashCoins, setCashCoins] = useState<{[k:string]:number}>({
    '1': 0, '0.5': 0, '0.25': 0, '0.1': 0, '0.05': 0, '0.01': 0
  });
  const [caixaDate, setCaixaDate] = useState(() => new Date().toISOString().slice(0,10));
  const [caixaClosed, setCaixaClosed] = useState(false);
  const [savingCount, setSavingCount] = useState(false);
  const [countSaved, setCountSaved] = useState(false);

  const load = async () => {
    setLoading(true);
    try {
      setOperations(await fetchCaixaOperations());
    } catch {
      setError('Erro ao buscar operações de caixa');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { load(); }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    if (!amount || !type) return;
    setLoading(true);
    try {
      await createCaixaOperation({ type, amount: Number(amount), description });
      setAmount('');
      setDescription('');
      setType('suprimento');
      load();
    } catch {
      setError('Erro ao registrar operação');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Deseja remover esta operação?')) return;
    try {
      await deleteCaixaOperation(id);
      load();
    } catch {
      setError('Erro ao remover operação');
    }
  };

  const calcTotal = () => {
    let total = 0;
    Object.entries(cashNotes).forEach(([k, v]) => { total += Number(k) * v; });
    Object.entries(cashCoins).forEach(([k, v]) => { total += Number(k) * v; });
    return total;
  };

  // Carregar contagem ao abrir aba
  useEffect(() => {
    if (activeTab === 'count') {
      fetchCaixaCount(caixaDate).then((data) => {
        if (data) {
          setCashNotes(data.notes);
          setCashCoins(data.coins);
          setCaixaClosed(!!data.closed);
        } else {
          setCashNotes({ '200':0,'100':0,'50':0,'20':0,'10':0,'5':0,'2':0 });
          setCashCoins({ '1':0,'0.5':0,'0.25':0,'0.1':0,'0.05':0,'0.01':0 });
          setCaixaClosed(false);
        }
        setCountSaved(false);
      });
    }
  }, [activeTab, caixaDate]);

  // Salvar contagem
  const handleSaveCount = async () => {
    setSavingCount(true);
    setError(null);
    try {
      await saveCaixaCount({ date: caixaDate, notes: cashNotes, coins: cashCoins, total: calcTotal() });
      setCountSaved(true);
    } catch {
      setError('Erro ao salvar contagem do caixa');
    } finally {
      setSavingCount(false);
    }
  };

  // Fechar o dia
  const handleCloseDay = async () => {
    setError(null);
    try {
      await closeCaixaDay(caixaDate);
      setCaixaClosed(true);
    } catch {
      setError('Erro ao fechar o dia');
    }
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">Gestão do Caixa</h2>
      <div className="flex gap-2 mb-4">
        <button onClick={()=>setActiveTab('operations')} className={`px-3 py-2 rounded-t ${activeTab==='operations'?'bg-indigo-600 text-white':'bg-gray-200 text-gray-700'}`}>Operações</button>
        <button onClick={()=>setActiveTab('count')} className={`px-3 py-2 rounded-t ${activeTab==='count'?'bg-indigo-600 text-white':'bg-gray-200 text-gray-700'}`}>Contagem de Caixa</button>
      </div>
      {activeTab==='operations' && (
        <>

      <form className="mb-6 flex flex-col md:flex-row gap-2 md:items-end" onSubmit={handleSubmit}>
        <select value={type} onChange={e => setType(e.target.value as 'suprimento' | 'sangria')} className="border rounded p-2">
          <option value="suprimento">Suprimento</option>
          <option value="sangria">Sangria</option>
        </select>
        <input
          type="number"
          step="0.01"
          min="0"
          placeholder="Valor"
          value={amount}
          onChange={e => setAmount(e.target.value)}
          className="border rounded p-2 flex-1"
        />
        <input
          type="text"
          placeholder="Descrição (opcional)"
          value={description}
          onChange={e => setDescription(e.target.value)}
          className="border rounded p-2 flex-1"
        />
        <button type="submit" className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Registrar</button>
      </form>
      {error && <div className="bg-red-100 text-red-700 rounded p-2 mb-2">{error}</div>}
      {loading ? (
        <div>Carregando operações...</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full border bg-white rounded">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-2 py-1">Tipo</th>
                <th className="px-2 py-1">Valor</th>
                <th className="px-2 py-1">Descrição</th>
                <th className="px-2 py-1">Data/Hora</th>
                <th className="px-2 py-1">Ações</th>
              </tr>
            </thead>
            <tbody>
              {operations.map(op => (
                <tr key={op.id} className={op.type === 'suprimento' ? 'bg-green-50' : 'bg-red-50'}>
                  <td className="px-2 py-1 font-semibold flex items-center gap-1">
                    {op.type === 'suprimento' ? <ArrowUp className="inline h-4 w-4 text-green-600" /> : <ArrowDown className="inline h-4 w-4 text-red-600" />} {op.type}
                  </td>
                  <td className="px-2 py-1">R$ {op.amount.toFixed(2)}</td>
                  <td className="px-2 py-1">{op.description}</td>
                  <td className="px-2 py-1">{format(new Date(op.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}</td>
                  <td className="px-2 py-1 flex gap-1">
                    <button className="p-1 rounded hover:bg-gray-100" title="Imprimir Recibo" onClick={() => setPrintOp(op)}>
                      <Printer className="h-4 w-4" />
                    </button>
                    <button className="p-1 rounded hover:bg-red-100" title="Remover" onClick={() => handleDelete(op.id)}>
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </button>
                  </td>
                </tr>
              ))}
              {operations.length === 0 && (
                <tr><td colSpan={5} className="text-center text-gray-400 py-4">Nenhuma operação registrada</td></tr>
              )}
            </tbody>
          </table>
        </div>
      )}
      {printOp && <OperationReceipt operation={printOp} onClose={() => setPrintOp(null)} />}
        </>
      )}
      {activeTab==='count' && (
        <div className="bg-white rounded shadow p-4">
          <div className="flex flex-col md:flex-row md:items-end gap-4 mb-2">
            <div>
              <h3 className="font-semibold mb-2">Contagem de Caixa</h3>
            </div>
            <div className="flex-1 text-right">
              <label className="mr-2 font-medium">Data:</label>
              <input type="date" value={caixaDate} onChange={e=>setCaixaDate(e.target.value)} className="border rounded p-1" max={new Date().toISOString().slice(0,10)} />
            </div>
          </div>
          <div className="flex flex-col md:flex-row gap-8">
            <div className="flex-1">
              <h4 className="font-semibold mb-1">Notas</h4>
              {Object.keys(cashNotes).map(nota => (
                <div key={nota} className="flex items-center mb-2">
                  <label className="w-10">R$ {nota}</label>
                  <input
                    type="number"
                    min="0"
                    value={cashNotes[nota]}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCashNotes(c => ({ ...c, [nota]: Number(e.target.value) }))}
                    className="border rounded p-1 w-20 ml-2"
                    disabled={caixaClosed}
                  />
                  <span className="ml-2 text-gray-500">x R$ {nota} = R$ {(Number(nota)*cashNotes[nota]).toFixed(2)}</span>
                </div>
              ))}
            </div>
            <div className="flex-1">
              <h4 className="font-semibold mb-1">Moedas</h4>
              {Object.keys(cashCoins).map(moeda => (
                <div key={moeda} className="flex items-center mb-2">
                  <label className="w-10">R$ {moeda.replace('.', ',')}</label>
                  <input
                    type="number"
                    min="0"
                    value={cashCoins[moeda]}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCashCoins(c => ({ ...c, [moeda]: Number(e.target.value) }))}
                    className="border rounded p-1 w-20 ml-2"
                    disabled={caixaClosed}
                  />
                  <span className="ml-2 text-gray-500">x R$ {moeda.replace('.', ',')} = R$ {(Number(moeda)*cashCoins[moeda]).toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="mt-4 text-lg font-bold text-right">
            Total contado: R$ {calcTotal().toFixed(2)}
          </div>
          <div className="flex flex-col md:flex-row gap-4 mt-4 items-end">
            <div className="flex-1">
              {caixaClosed ? (
                <span className="text-green-700 font-semibold">Dia fechado</span>
              ) : countSaved ? (
                <span className="text-green-700">Contagem salva!</span>
              ) : null}
            </div>
            <div className="flex gap-2">
              {!caixaClosed && (
                <>
                  <button onClick={handleSaveCount} className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 disabled:opacity-60" disabled={savingCount} type="button">Salvar Contagem</button>
                  <button onClick={handleCloseDay} className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700" type="button">Fechar o Dia</button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
