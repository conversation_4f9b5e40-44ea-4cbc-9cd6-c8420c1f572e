// API para contagem de caixa (frontend)
export interface CaixaCount {
  date: string; // yyyy-MM-dd
  notes: Record<string, number>;
  coins: Record<string, number>;
  total: number;
  closed?: number;
}

export async function fetchCaixaCount(date: string): Promise<CaixaCount|null> {
  const res = await fetch(`/api/caixa-count/${date}`);
  if (!res.ok) return null;
  return res.json();
}

export async function saveCaixaCount(data: CaixaCount): Promise<void> {
  const res = await fetch('/api/caixa-count', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!res.ok) throw new Error('Erro ao salvar contagem do caixa');
}

export async function closeCaixaDay(date: string): Promise<void> {
  const res = await fetch('/api/caixa-count/close', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ date }),
  });
  if (!res.ok) throw new Error('Erro ao fechar o dia');
}
