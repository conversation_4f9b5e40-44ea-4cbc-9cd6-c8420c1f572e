// API para contagem de caixa usando Supabase
import { supabase } from './supabase';

export interface CaixaCount {
  date: string; // yyyy-MM-dd
  notes: Record<string, number>;
  coins: Record<string, number>;
  total: number;
  closed?: number;
}

export async function fetchCaixaCount(date: string): Promise<CaixaCount|null> {
  const { data, error } = await supabase
    .from('caixa_counts')
    .select('*')
    .eq('date', date)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
  return data || null;
}

export async function saveCaixaCount(data: CaixaCount): Promise<void> {
  const { error } = await supabase
    .from('caixa_counts')
    .upsert([data], { onConflict: 'date' });

  if (error) throw error;
}

export async function closeCaixaDay(date: string): Promise<void> {
  const { error } = await supabase
    .from('caixa_counts')
    .update({ closed: 1 })
    .eq('date', date);

  if (error) throw error;
}
