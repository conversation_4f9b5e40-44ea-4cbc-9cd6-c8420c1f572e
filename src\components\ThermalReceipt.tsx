import { useEffect } from 'react';
import type { Order } from '../lib/api';

interface ThermalReceiptProps {
  order: Order;
  onClose: () => void;
  copies?: number; // Número de cópias a imprimir (padrão: 1)
}

export default function ThermalReceipt({ order, onClose, copies = 1 }: ThermalReceiptProps) {
  useEffect(() => {
    // Atraso pequeno para garantir que o DOM foi renderizado
    const timeoutId = setTimeout(() => {
      // Adicionar classe ao body para controlar a impressão
      document.body.classList.add('printing-receipt');
      window.print();
      // Remover classe após impressão
      setTimeout(() => {
        document.body.classList.remove('printing-receipt');
      }, 1000);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []); // Executa apenas uma vez na montagem

  const handlePrintAgain = () => {
    document.body.classList.add('printing-receipt');
    window.print();
    setTimeout(() => {
      document.body.classList.remove('printing-receipt');
    }, 1000);
  };

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return 'R$ 0,00';
    // Arredonda para 2 casas decimais e converte para string com vírgula
    return `R$ ${(Math.round(value * 100) / 100).toFixed(2).replace('.', ',')}`;
  };

  // Garantir que address está presente
  if (!order.address) return null;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 no-print-modal">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full no-print-modal">
        <div className="p-4 no-print-modal">
          <div className="receipt">
            <style>
              {`
                @media print {
                  /* Quando estamos imprimindo a etiqueta */
                  body.printing-receipt * {
                    visibility: hidden !important;
                  }

                  /* Mostrar apenas o conteúdo da etiqueta */
                  body.printing-receipt .receipt,
                  body.printing-receipt .receipt * {
                    visibility: visible !important;
                  }

                  /* Posicionar a etiqueta para impressão */
                  body.printing-receipt .receipt {
                    position: fixed !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 80mm !important;
                    padding: 5mm !important;
                    margin: 0 !important;
                    background: white !important;
                    z-index: 9999 !important;
                  }

                  /* Esconder botões e elementos de interface */
                  body.printing-receipt .no-print {
                    display: none !important;
                  }

                  /* Fallback para impressão normal */
                  body:not(.printing-receipt) .receipt {
                    width: 80mm;
                    padding: 5mm;
                  }

                  .no-print {
                    display: none !important;
                  }
                  /* Alto contraste para impressão térmica */
                  .receipt * {
                    color: black !important;
                    font-weight: bold !important;
                    text-shadow: none !important;
                  }
                  .receipt img {
                    filter: contrast(200%) brightness(0) !important;
                  }
                  /* Garantir que o valor a pagar fique em negrito na impressão */
                  .receipt p {
                    color: black !important;
                    font-weight: bold !important;
                    margin: 0.3em 0 !important;
                  }
                  .receipt-divider {
                    margin: 0.5em 0 !important;
                    border-top: 1px solid black !important;
                  }
                }
                .receipt {
                  font-family: monospace;
                  width: 80mm;
                  margin: 0 auto;
                  padding: 5mm;
                  text-align: center;
                }
                .receipt-header {
                  text-align: center;
                  margin-bottom: 0.5em;
                }
                .receipt-logo {
                  height: 32px;
                  width: auto;
                  margin: 0 auto 0.5em;
                  display: block;
                  /* Alto contraste na preview também */
                  filter: contrast(200%) brightness(0);
                }
                .receipt-divider {
                  border-top: 1px solid black;
                  margin: 0.5em 0;
                }
                .receipt p {
                  margin: 0.3em 0;
                }
              `}
            </style>

            <div className="receipt-header">
              <img
                src="/recibo.png"
                alt="Delivery CRM"
                className="receipt-logo"
                onError={(e) => {
                  const img = e.target as HTMLImageElement;
                  img.onerror = null; // Previne loop infinito
                  img.src = '/receipt-logo.svg';
                }}
              />
              <div>
                PEDIDO #{order.id.toString().slice(0, 8)}
                <br />
                {new Date(order.created_at).toLocaleString()}
              </div>
            </div>

            {/* Etiqueta de Envio */}
            <div className="receipt-label" style={{ textAlign: 'left', margin: '1em 0' }}>
              <p><strong>ETIQUETA DE ENVIO</strong></p>
              <p>{order.address.address}{order.address.complement ? ` - ${order.address.complement}` : ''}</p>
              <p>{order.address.neighborhood} - {order.address.city}</p>
            </div>

            <div className="receipt-divider" />

            <div style={{ textAlign: 'left' }}>
              <p><strong>Cliente:</strong> {order.customer_name}</p>
              <p><strong>Telefone:</strong> {order.customer_phone}</p>
              <p>
                <strong>Endereço:</strong> {order.address.address}
                {order.address.complement && ` - ${order.address.complement}`}
                <br />
                {order.address.neighborhood} - {order.address.city}
              </p>
            </div>

            <div className="receipt-divider" />

            <div style={{ textAlign: 'right', minHeight: '5em' }}>
              {order.payment_method === 'venda_do_site' ? (
                <p>Obrigado por comprar em newlookcosmeticos.com.br</p>
              ) : (
                <>
                  <p><strong>Produtos:</strong> {formatCurrency(order.products_total)}</p>
                  <p><strong>Entrega:</strong> {formatCurrency(order.delivery_fee)}</p>
                  <p><strong>Total:</strong> {formatCurrency(order.products_total + order.delivery_fee)}</p>
                  <p><strong>Forma de pagamento:</strong> {
                    order.payment_method === 'pix' ? 'PIX' :
                    order.payment_method === 'card' ? 'Cartão' :
                    'Dinheiro'
                  }</p>
                  {order.payment_method === 'cash' && !order.is_paid && order.change_amount && order.change_amount > 0 && (
                    <p><strong>Troco:</strong> {formatCurrency(order.change_amount)}</p>
                  )}
                </>
              )}
            </div>

            <div className="receipt-divider" />

            {/* Status de Pagamento em Destaque */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              border: '2px solid black',
              padding: '0.5em',
              margin: '0.5em 0'
            }}>
              {order.is_paid ? (
                <strong style={{ 
                  fontSize: '1.5em',
                  fontWeight: 'bold',
                  width: '100%',
                  textAlign: 'center',
                  letterSpacing: '0.1em'
                }}>
                  * PAGO *
                </strong>
              ) : (
                <>
                  <strong style={{ fontSize: '1.1em' }}>VALOR A PAGAR:</strong>
                  <span style={{ fontSize: '1.3em', fontWeight: 'bold' }}>
                    {formatCurrency(
                      order.payment_method === 'cash' 
                        ? order.payment_amount || (order.products_total + order.delivery_fee)
                        : order.products_total + order.delivery_fee
                    )}
                  </span>
                </>
              )}
            </div>
          </div>

          <div className="mt-4 flex justify-between items-center no-print">
            {copies > 1 && (
              <div className="text-sm text-gray-600">
                <p>📄 Imprimir {copies} vias</p>
                <button
                  onClick={handlePrintAgain}
                  className="mt-1 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                >
                  Imprimir Novamente
                </button>
              </div>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
