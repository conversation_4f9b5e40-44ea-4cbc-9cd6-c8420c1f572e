import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { Deliverer, PaymentMethod } from '../types';

interface OrderConstraint {
  column_name: string;
  constraint_name: string;
  constraint_definition: string;
}

export default function Configuracoes() {
  const [deliverers, setDeliverers] = useState<Deliverer[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [newDeliverer, setNewDeliverer] = useState('');
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    name: '',
    code: '',
    default_paid: false
  });
  const [activeTab, setActiveTab] = useState<'deliverers' | 'payments'>('deliverers');

  useEffect(() => {
    fetchDeliverers();
    fetchPaymentMethods();
    // fetchOrderConstraints();
  }, []);

  const fetchDeliverers = async () => {
    console.log('Buscando entregadores...');
    const { data, error } = await supabase.from('deliverers').select('*').order('name');
    console.log('Entregadores recebidos:', data);
    if (error) {
      console.error('Erro ao buscar entregadores:', error);
    }
    if (data) setDeliverers(data);
  };

  const fetchPaymentMethods = async () => {
    // Query simples para buscar todos os métodos de pagamento
    const { data, error } = await supabase
      .from('payment_methods')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching payment methods:', error);
      return;
    }

    setPaymentMethods(data || []);
  };

  const fetchOrderConstraints = async () => {
    const { data, error } = await supabase
      .rpc('get_order_constraints');

    if (error) {
      console.error('Error fetching order constraints:', error);
      return;
    }

    const constraints = data as OrderConstraint[];
    console.log('Order constraints:', constraints);

    // Você pode usar as constraints aqui para mostrar na interface
    // Por exemplo:
    constraints.forEach(constraint => {
      console.log(`Column ${constraint.column_name} has constraint: ${constraint.constraint_definition}`);
    });
  };

  const handleAddDeliverer = async () => {
    if (!newDeliverer.trim()) return;

    console.log('Adicionando entregador:', newDeliverer.trim());
    const result = await supabase
      .from('deliverers')
      .insert([{ name: newDeliverer.trim() }]);

    console.log('Resultado da adição:', result);

    if (!result.error) {
      setNewDeliverer('');
      fetchDeliverers();
    } else {
      console.error('Erro ao adicionar entregador:', result.error);
    }
  };

  const handleAddPaymentMethod = async () => {
    if (!newPaymentMethod.name.trim() || !newPaymentMethod.code.trim()) return;

    const { error } = await supabase
      .from('payment_methods')
      .insert([{
        name: newPaymentMethod.name.trim(),
        code: newPaymentMethod.code.trim().toLowerCase(),
        default_paid: newPaymentMethod.default_paid
      }]);

    if (!error) {
      setNewPaymentMethod({ name: '', code: '', default_paid: false });
      fetchPaymentMethods();
    }
  };

  const toggleDelivererStatus = async (deliverer: Deliverer) => {
    const { error } = await supabase
      .from('deliverers')
      .update({ is_active: !deliverer.is_active })
      .eq('id', deliverer.id);

    if (!error) fetchDeliverers();
  };

  const togglePaymentMethodStatus = async (method: PaymentMethod) => {
    const { error } = await supabase
      .from('payment_methods')
      .update({ is_active: !method.is_active })
      .eq('id', method.id);

    if (!error) fetchPaymentMethods();
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white shadow rounded-lg">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('deliverers')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                activeTab === 'deliverers'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Entregadores
            </button>
            <button
              onClick={() => setActiveTab('payments')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                activeTab === 'payments'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Métodos de Pagamento
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Entregadores */}
          {activeTab === 'deliverers' && (
            <div>
              <div className="flex gap-4 mb-6">
                <input
                  type="text"
                  value={newDeliverer}
                  onChange={(e) => setNewDeliverer(e.target.value)}
                  placeholder="Nome do entregador"
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={handleAddDeliverer}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Adicionar
                </button>
              </div>

              <div className="space-y-4">
                {deliverers.map((deliverer) => (
                  <div
                    key={deliverer.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-md"
                  >
                    <span className="font-medium">{deliverer.name}</span>
                    <button
                      onClick={() => toggleDelivererStatus(deliverer)}
                      className={`px-4 py-2 rounded-md ${
                        deliverer.is_active
                          ? 'bg-green-100 text-green-700 hover:bg-green-200'
                          : 'bg-red-100 text-red-700 hover:bg-red-200'
                      }`}
                    >
                      {deliverer.is_active ? 'Ativo' : 'Inativo'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Métodos de Pagamento */}
          {activeTab === 'payments' && (
            <div>
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="space-y-1">
                  <input
                    type="text"
                    value={newPaymentMethod.name}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      name: e.target.value,
                      code: e.target.value.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/[^a-z0-9]+/g, '_')
                    })}
                    placeholder="Nome do método"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                  <div className="text-sm text-gray-500">
                    Código: {newPaymentMethod.code || '(será gerado)'}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="defaultPaid"
                    checked={newPaymentMethod.default_paid}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      default_paid: e.target.checked
                    })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="defaultPaid" className="text-sm text-gray-700">
                    Pago por padrão
                  </label>
                </div>
                <button
                  onClick={handleAddPaymentMethod}
                  disabled={!newPaymentMethod.name.trim()}
                  className={`
                    px-4 py-2 rounded-md
                    ${newPaymentMethod.name.trim()
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'}
                  `}
                >
                  Adicionar
                </button>
              </div>

              <div className="space-y-4">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-md"
                  >
                    <div>
                      <span className="font-medium">{method.name}</span>
                      <span className="ml-2 text-sm text-gray-500">
                        ({method.code})
                        {method.default_paid ? ' • Pago por padrão' : ''}
                      </span>
                    </div>
                    <button
                      onClick={() => togglePaymentMethodStatus(method)}
                      className={`px-4 py-2 rounded-md ${
                        method.is_active
                          ? 'bg-green-100 text-green-700 hover:bg-green-200'
                          : 'bg-red-100 text-red-700 hover:bg-red-200'
                      }`}
                    >
                      {method.is_active ? 'Ativo' : 'Inativo'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}




