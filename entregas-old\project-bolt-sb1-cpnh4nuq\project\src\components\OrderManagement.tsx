import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { Order, OrderStatus, Deliverer } from '../types';
import { format, startOfDay, endOfDay, isToday, addDays, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Printer, ChevronLeft, ChevronRight, Truck } from 'lucide-react';
import ThermalReceipt from './ThermalReceipt';

const statusConfig = {
  generated: {
    label: 'Gerado',
    color: 'bg-yellow-100 text-yellow-800',
    hoverColor: 'hover:bg-yellow-200'
  },
  sent: {
    label: 'Enviado',
    color: 'bg-blue-100 text-blue-800',
    hoverColor: 'hover:bg-blue-200'
  },
  returned: {
    label: 'Retornado',
    color: 'bg-purple-100 text-purple-800',
    hoverColor: 'hover:bg-purple-200'
  },
  cancelled: {
    label: 'Desistência',
    color: 'bg-red-200 text-red-900',
    hoverColor: 'hover:bg-red-300'
  }
};

const statusOrder: OrderStatus[] = ['cancelled', 'generated', 'sent', 'returned'];

export default function OrderManagement() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [deliverers, setDeliverers] = useState<Deliverer[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [editingDelivererId, setEditingDelivererId] = useState<string | null>(null);
  const [editingValues, setEditingValues] = useState<{orderId: string, field: 'products_total' | 'delivery_fee', value: number} | null>(null);
  // Armazenar informações sobre datas com entregas e alertas
  const [dateInfo, setDateInfo] = useState<{
    [date: string]: {
      hasOrders: boolean;
      hasAlerts: boolean;
    }
  }>({});

  // Estado para controlar o modal de confirmação
  const [confirmationModal, setConfirmationModal] = useState<{
    show: boolean;
    order: Order | null;
    newStatus: OrderStatus | null;
    paymentAmount: number | null;
  }>({ show: false, order: null, newStatus: null, paymentAmount: null });

  const fetchOrders = async () => {
    setLoading(true);
    try {
      // Buscar pedidos do dia selecionado
      const { data: dayOrders, error } = await supabase
        .from('orders')
        .select('*')
        .gte('created_at', startOfDay(selectedDate).toISOString())
        .lt('created_at', endOfDay(selectedDate).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;
      setOrders(dayOrders || []);

      // Buscar informações de pedidos para os próximos e anteriores dias
      // para destacar dias com entregas e alertas no carrossel
      const newDateInfo: {[date: string]: {hasOrders: boolean; hasAlerts: boolean}} = {};
      
      // Buscar todos os pedidos em um intervalo maior (5 dias para trás e 5 para frente)
      const startDate = subDays(selectedDate, 5);
      const endDate = addDays(selectedDate, 5);
      
      const { data: rangeOrders, error: rangeError } = await supabase
        .from('orders')
        .select('*')
        .gte('created_at', startOfDay(startDate).toISOString())
        .lt('created_at', endOfDay(endDate).toISOString());
        
      if (rangeError) throw rangeError;
      
      // Inicializar todas as datas no intervalo
      for (let i = -5; i <= 5; i++) {
        const date = addDays(selectedDate, i);
        const dateStr = date.toDateString();
        newDateInfo[dateStr] = { hasOrders: false, hasAlerts: false };
      }
      
      // Processar os pedidos para identificar dias com entregas e alertas
      if (rangeOrders) {
        rangeOrders.forEach(order => {
          const orderDate = new Date(order.created_at).toDateString();
          
          // Marcar que este dia tem pedidos
          if (!newDateInfo[orderDate]) {
            newDateInfo[orderDate] = { hasOrders: false, hasAlerts: false };
          }
          newDateInfo[orderDate].hasOrders = true;
          
          // Verificar se é um pedido enviado em dinheiro que não retornou
          if (order.status === 'sent' && order.payment_method === 'cash' && order.payment_amount && order.payment_amount > 0) {
            newDateInfo[orderDate].hasAlerts = true;
          }
        });
      }
      
      setDateInfo(newDateInfo);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDeliverers = async () => {
    try {
      const { data } = await supabase
        .from('deliverers')
        .select('*')
        .eq('is_active', true)
        .order('name');
      
      setDeliverers(data || []);
    } catch (error) {
      console.error('Error fetching deliverers:', error);
    }
  };

  useEffect(() => {
    fetchOrders();
    fetchDeliverers();
  }, [selectedDate]);

  const handleStatusChange = async (order: Order, newStatus: OrderStatus) => {
    if (newStatus === 'cancelled' && order.status === 'cancelled') return;
    
    try {
      setLoading(true);
      
      // Caso especial: permitir mudar de enviado para retornado mesmo em dias anteriores
      const isReturnCase = order.status === 'sent' && newStatus === 'returned' && order.payment_method === 'cash';
      
      // Não permitir alterar status de pedidos de dias anteriores (exceto para o caso especial)
      if (!isToday(new Date(order.created_at)) && !isReturnCase) {
        alert('Apenas pedidos do dia atual podem ter o status alterado');
        return;
      }
      
      // Não permitir status 'returned' para pedidos que não são em dinheiro
      if (newStatus === 'returned' && order.payment_method !== 'cash') {
        alert('Apenas pedidos em dinheiro podem ser retornados');
        return;
      }
      
      // Para o caso especial de retorno retroativo, mostrar confirmação
      if (isReturnCase && !isToday(new Date(order.created_at))) {
        setConfirmationModal({
          show: true,
          order,
          newStatus,
          paymentAmount: order.payment_amount || 0
        });
        setLoading(false);
        return;
      }
      
      // Mapear status para valores aceitos pelo banco de dados
      // O banco de dados pode estar esperando 'delivered' para pedidos cancelados
      let statusToSave = newStatus;
      if (newStatus === 'cancelled') {
        statusToSave = 'delivered' as any; // Usar 'as any' para evitar erro de tipo
      }
      
      // Atualizar o status no banco de dados
      const { error } = await supabase
        .from('orders')
        .update({ status: statusToSave })
        .eq('id', order.id);
      
      if (error) throw error;
      
      // Atualizar a lista de pedidos com o status visual correto
      setOrders(prevOrders => 
        prevOrders.map(o => 
          o.id === order.id ? { ...o, status: newStatus } : o
        )
      );
      
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      alert('Erro ao atualizar status do pedido');
    } finally {
      setLoading(false);
    }
  };
  
  // Função para confirmar a mudança de status para retornado
  const confirmStatusChange = async () => {
    if (!confirmationModal.order || !confirmationModal.newStatus) return;
    
    try {
      setLoading(true);
      
      // Atualizar o status no banco de dados
      const { error } = await supabase
        .from('orders')
        .update({ status: confirmationModal.newStatus })
        .eq('id', confirmationModal.order.id);
      
      if (error) throw error;
      
      // Atualizar a lista de pedidos com o status visual correto
      setOrders(prevOrders => 
        prevOrders.map(o => 
          o.id === confirmationModal.order?.id ? { ...o, status: confirmationModal.newStatus as OrderStatus } : o
        )
      );
      
      // Fechar o modal
      setConfirmationModal({ show: false, order: null, newStatus: null, paymentAmount: null });
      
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      alert('Erro ao atualizar status do pedido');
    } finally {
      setLoading(false);
    }
  };

  const handleDelivererChange = async (order: Order, delivererId: string) => {
    // Não permitir mudança de entregador para pedidos de outros dias
    if (!isToday(new Date(order.created_at))) return;

    try {
      const { error } = await supabase
        .from('orders')
        .update({ deliverer_id: delivererId })
        .eq('id', order.id);

      if (error) throw error;

      // Atualizar a lista localmente
      setOrders(orders.map(o => 
        o.id === order.id ? { ...o, deliverer_id: delivererId } : o
      ));
      
      // Fechar o seletor de entregador
      setEditingDelivererId(null);
    } catch (error) {
      console.error('Error updating order deliverer:', error);
    }
  };

  const handleValueChange = async (order: Order, field: 'products_total' | 'delivery_fee', value: number) => {
    // Não permitir mudança de valores para pedidos de outros dias
    if (!isToday(new Date(order.created_at))) return;

    try {
      const { error } = await supabase
        .from('orders')
        .update({ [field]: value })
        .eq('id', order.id);

      if (error) throw error;

      // Atualizar a lista localmente
      setOrders(orders.map(o => 
        o.id === order.id ? { ...o, [field]: value } : o
      ));
      
      // Fechar o editor de valores
      setEditingValues(null);
    } catch (error) {
      console.error(`Error updating order ${field}:`, error);
    }
  };

  const changeDate = (days: number) => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + days);
    setSelectedDate(newDate);
  };

  if (selectedOrder) {
    return <ThermalReceipt order={selectedOrder} onClose={() => setSelectedOrder(null)} />;
  }

  return (
    <div className="space-y-4">
      {/* Modal de confirmação para mudança de status retroativa */}
      {confirmationModal.show && confirmationModal.order && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Confirmar alteração de status</h3>
            <p className="text-gray-600 mb-4">
              Você está alterando o status de um pedido de <span className="font-medium">Enviado</span> para <span className="font-medium">Retornado</span> de uma data anterior.              
            </p>
            <p className="text-gray-600 mb-4">
              Confirme que o motoboy trouxe a quantia de <span className="font-medium text-green-600">R$ {confirmationModal.paymentAmount?.toFixed(2)}</span> referente a este pedido.
            </p>
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Esta ação não pode ser desfeita. Uma vez que o status for alterado para Retornado, não será possível voltar ao status anterior.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setConfirmationModal({ show: false, order: null, newStatus: null, paymentAmount: null })}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={confirmStatusChange}
                className="px-4 py-2 bg-indigo-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-indigo-700"
              >
                Confirmar
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white shadow-sm rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Gestão de Pedidos</h2>
          
          {/* Carrossel de datas */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => changeDate(-5)}
              className="p-1 rounded-full hover:bg-gray-100"
              aria-label="Cinco dias para trás"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            
            {/* Dias anteriores */}
            {[-2, -1, 0, 1, 2].map(offset => {
              const date = addDays(selectedDate, offset);
              const isSelected = offset === 0;
              const isToday = new Date().toDateString() === date.toDateString();
              const dateStr = date.toDateString();
              const hasOrders = dateInfo[dateStr]?.hasOrders;
              const hasAlerts = dateInfo[dateStr]?.hasAlerts;
              
              return (
                <button
                  key={offset}
                  onClick={() => setSelectedDate(date)}
                  className={`
                    flex flex-col items-center justify-center p-1 rounded-lg min-w-[60px] relative
                    ${isSelected ? 'bg-indigo-100 text-indigo-800' : ''}
                    ${!hasOrders ? 'text-gray-400' : ''}
                    ${hasOrders && !isSelected ? 'text-gray-700' : ''}
                    ${isToday && !isSelected ? 'border border-indigo-300' : ''}
                  `}
                >
                  {hasAlerts && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full border border-white"></div>
                  )}
                  <span className={`text-lg font-bold ${isSelected ? 'text-indigo-800' : ''}`}>
                    {format(date, 'dd/MM')}
                  </span>
                  <span className={`text-xs ${isSelected ? 'text-indigo-600' : 'text-gray-500'}`}>
                    {format(date, 'EEEE', { locale: ptBR }).slice(0, 3)}
                  </span>
                </button>
              );
            })}
            
            <button
              onClick={() => changeDate(5)}
              className="p-1 rounded-full hover:bg-gray-100"
              aria-label="Cinco dias para frente"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
            
            {/* Botão para voltar ao dia atual */}
            <button
              onClick={() => setSelectedDate(new Date())}
              className="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
              title="Voltar para hoje"
            >
              Hoje
            </button>
          </div>
        </div>
        
        {loading ? (
          <div className="text-center py-4">Carregando pedidos...</div>
        ) : (
          <div className="space-y-2">
            {orders.map((order) => {
              const isCurrentDay = isToday(new Date(order.created_at));
              const isCancelled = order.status === 'cancelled';
              
              // Determina se o pedido está no último status disponível
              const isLastStatus = order.payment_method === 'cash' 
                ? order.status === 'returned' 
                : (order.status === 'cancelled' || order.status === 'sent');

              return (
                <div 
                  key={order.id} 
                  className={`
                    border rounded-lg p-2 
                    ${isCurrentDay ? 'bg-white' : 'bg-gray-50'}
                    ${isCancelled ? 'opacity-60' : ''}
                    ${isLastStatus && !isCancelled ? 'border-green-500 border-2 bg-green-50/30' : ''}
                    ${isCancelled ? 'border-red-500 border-2' : ''}
                  `}
                >
                  <div className={`flex justify-between items-start gap-2 ${isCancelled || (isLastStatus && !isCancelled) ? 'opacity-50' : ''}`}>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">#{order.id.slice(0, 8)} - {order.customer_name}</div>
                          <div className="text-sm text-gray-500 mt-0.5">
                            {order.customer_phone?.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3')}
                          </div>
                          <div className="text-sm text-gray-600 mt-0.5">
                            {order.address.street}, {order.address.number} - {order.address.neighborhood}
                          </div>
                          
                          {/* Entregador */}
                          <div className="flex items-center mt-1">
                            <Truck className="h-3.5 w-3.5 text-gray-500 mr-1" />
                            {editingDelivererId === order.id ? (
                              <select
                                value={order.deliverer_id || ''}
                                onChange={(e) => handleDelivererChange(order, e.target.value)}
                                className="text-sm p-1 border border-gray-300 rounded"
                                autoFocus
                                onBlur={() => setEditingDelivererId(null)}
                              >
                                <option value="">Selecione um entregador</option>
                                {deliverers.map((deliverer) => (
                                  <option key={deliverer.id} value={deliverer.id}>
                                    {deliverer.name}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <div 
                                className="text-sm text-gray-700 cursor-pointer hover:text-indigo-600 hover:underline flex items-center"
                                onClick={() => isToday(new Date(order.created_at)) && setEditingDelivererId(order.id)}
                                title={isToday(new Date(order.created_at)) ? 'Clique para alterar o entregador' : 'Apenas pedidos do dia atual podem ter o entregador alterado'}
                              >
                                {deliverers.find(d => d.id === order.deliverer_id)?.name || 'Sem entregador'}
                                {isToday(new Date(order.created_at)) && <div className="ml-1 text-xs text-indigo-600">(editar)</div>}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          {order.payment_method === 'venda_do_site' ? (
                            <div className="font-medium">Venda do Site</div>
                          ) : (
                            <div className="flex flex-col items-end">
                              <div className="flex items-center">
                                <div className="text-sm mr-1">Produtos:</div>
                                {editingValues?.orderId === order.id && editingValues.field === 'products_total' ? (
                                  <input 
                                    type="number" 
                                    value={editingValues.value} 
                                    onChange={(e) => setEditingValues({...editingValues, value: parseFloat(e.target.value) || 0})}
                                    onBlur={() => handleValueChange(order, 'products_total', editingValues.value)}
                                    className="w-16 p-0.5 text-sm border border-gray-300 rounded"
                                    autoFocus
                                    step="0.01"
                                    min="0"
                                  />
                                ) : (
                                  <div 
                                    className="cursor-pointer hover:text-indigo-600 hover:underline"
                                    onClick={() => isToday(new Date(order.created_at)) && setEditingValues({orderId: order.id, field: 'products_total', value: order.products_total})}
                                    title={isToday(new Date(order.created_at)) ? 'Clique para editar o valor dos produtos' : 'Apenas pedidos do dia atual podem ser editados'}
                                  >
                                    R$ {order.products_total.toFixed(2)}
                                  </div>
                                )}
                              </div>
                              
                              <div className="flex items-center">
                                <div className="text-sm mr-1">Entrega:</div>
                                {editingValues?.orderId === order.id && editingValues.field === 'delivery_fee' ? (
                                  <input 
                                    type="number" 
                                    value={editingValues.value} 
                                    onChange={(e) => setEditingValues({...editingValues, value: parseFloat(e.target.value) || 0})}
                                    onBlur={() => handleValueChange(order, 'delivery_fee', editingValues.value)}
                                    className="w-16 p-0.5 text-sm border border-gray-300 rounded"
                                    autoFocus
                                    step="0.01"
                                    min="0"
                                  />
                                ) : (
                                  <div 
                                    className="cursor-pointer hover:text-indigo-600 hover:underline"
                                    onClick={() => isToday(new Date(order.created_at)) && setEditingValues({orderId: order.id, field: 'delivery_fee', value: order.delivery_fee})}
                                    title={isToday(new Date(order.created_at)) ? 'Clique para editar o valor da entrega' : 'Apenas pedidos do dia atual podem ser editados'}
                                  >
                                    R$ {order.delivery_fee.toFixed(2)}
                                  </div>
                                )}
                              </div>
                              
                              <div className="font-medium mt-1 border-t border-gray-200 pt-1">
                                <div className="text-sm mr-1">Total:</div>
                                <div>R$ {(order.products_total + order.delivery_fee).toFixed(2)}</div>
                              </div>
                            </div>
                          )}
                          <div className="text-sm text-gray-500 mt-0.5">
                            {order.payment_method === 'venda_do_site' 
                              ? 'Venda do Site'
                              : order.payment_method === 'cash'
                                ? 'Dinheiro'
                                : order.payment_method === 'card'
                                  ? 'Cartão'
                                  : order.payment_method_name || order.payment_method
                            }
                          </div>
                          {order.payment_method === 'cash' && (
                            <>
                              <div className="text-sm text-gray-500 mt-0.5">
                                {order.status === 'sent' && order.payment_amount && order.payment_amount > 0 ? (
                                  <div className="flex items-center bg-yellow-100 px-2 py-1 rounded-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <div className="font-medium text-yellow-800">Motoboy trazer: R$ {order.payment_amount.toFixed(2)}</div>
                                  </div>
                                ) : (
                                  <div>Motoboy trazer: R$ {order.payment_amount?.toFixed(2)}</div>
                                )}
                              </div>
                            </>
                          )}
                          <div className="text-sm text-gray-500 mt-0.5">
                            {format(new Date(order.created_at), 'HH:mm')}
                          </div>
                          <button
                            onClick={() => setSelectedOrder(order)}
                            className="mt-1 p-1.5 text-indigo-700 rounded-md hover:bg-indigo-50"
                            title="Imprimir Recibo"
                          >
                            <Printer className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 flex gap-1">
                    {statusOrder.map((status) => {
                      // Não mostrar o botão de retornado se não for pagamento em dinheiro
                      if (status === 'returned' && order.payment_method !== 'cash') {
                        return null;
                      }
                      
                      const isDisabled = !isCurrentDay;
                      const config = statusConfig[status];
                      const isActive = status === order.status;
                      
                      return (
                        <button
                          key={status}
                          onClick={() => !isDisabled && handleStatusChange(order, status)}
                          disabled={isDisabled}
                          className={`
                            flex-1 px-4 py-2 rounded text-sm font-medium
                            ${isActive ? config.color : 'bg-gray-50 text-gray-400'}
                            ${!isDisabled && !isActive ? config.hoverColor : ''}
                            ${isDisabled ? 'opacity-50 cursor-default' : 'cursor-pointer'}
                            transition-colors duration-150
                          `}
                          title={
                            !isCurrentDay ? 'Pedidos de outros dias não podem ter o status alterado' :
                            ''
                          }
                        >
                          {config.label}
                        </button>
                      );
                    })}
                  </div>
                </div>
              );
            })}
            {orders.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                Nenhum pedido encontrado
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
