const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'database/local.db'));

const migrations = [
  // Criação fiel ao modelo Supabase
  `CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    last_order_at TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    street TEXT NOT NULL,
    number TEXT NOT NULL,
    complement TEXT,
    neighborhood TEXT NOT NULL,
    city TEXT NOT NULL,
    is_default INTEGER DEFAULT 0,
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY(customer_id) REFERENCES customers(id)
  );`,
  `CREATE TABLE IF NOT EXISTS deliverers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT (datetime('now'))
  );`,
  `CREATE TABLE IF NOT EXISTS payment_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    default_paid INTEGER DEFAULT 0,
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT (datetime('now'))
  );`,
  `CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    address_id INTEGER,
    products_total REAL NOT NULL,
    delivery_fee REAL NOT NULL,
    payment_method TEXT NOT NULL,
    payment_amount REAL,
    change_amount REAL,
    is_paid INTEGER NOT NULL DEFAULT 0,
    status TEXT NOT NULL,
    deliverer_id INTEGER NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY(customer_id) REFERENCES customers(id),
    FOREIGN KEY(address_id) REFERENCES addresses(id),
    FOREIGN KEY(deliverer_id) REFERENCES deliverers(id)
  );`
];

function runMigrations() {
  db.serialize(() => {
    let i = 0;
    function next() {
      if (i >= migrations.length) {
        console.log('Migração concluída.');
        db.close();
        return;
      }
      db.run(migrations[i], [], err => {
        if (err && !/duplicate|exists|already/.test(err.message)) {
          console.error('Erro na migração:', migrations[i], err.message);
        }
        i++;
        next();
      });
    }
    next();
  });
}

runMigrations();
