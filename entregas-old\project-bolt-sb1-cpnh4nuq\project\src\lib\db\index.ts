import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { v4 as uuidv4 } from 'uuid';

// Definir o esquema do banco de dados
interface DeliveryCRMDB extends DBSchema {
  customers: {
    key: string;
    value: {
      id: string;
      phone: string;
      name: string;
      created_at: string;
      last_order_at?: string | null;
    };
    indexes: { 'by-phone': string };
  };
  addresses: {
    key: string;
    value: {
      id: string;
      customer_id: string;
      street: string;
      number: string;
      complement?: string | null;
      neighborhood: string;
      city: string;
      is_default: boolean;
      created_at: string;
    };
    indexes: { 'by-customer': string };
  };
  deliverers: {
    key: string;
    value: {
      id: string;
      name: string;
      is_active: boolean;
      created_at: string;
    };
  };
  payment_methods: {
    key: string;
    value: {
      id: string;
      name: string;
      code: string;
      default_paid: boolean;
      is_active: boolean;
      created_at: string;
    };
    indexes: { 'by-code': string };
  };
  orders: {
    key: string;
    value: {
      id: string;
      customer_id: string;
      customer_name: string;
      customer_phone: string;
      address: any;
      products_total: number;
      delivery_fee: number;
      total_amount?: number;
      payment_method: string;
      payment_method_name?: string | null;
      payment_amount?: number | null;
      change_amount?: number | null;
      is_paid: boolean;
      status: string;
      deliverer_id?: string | null;
      created_at: string;
    };
    indexes: { 'by-customer': string; 'by-date': string };
  };
  reactivation_logs: {
    key: string;
    value: {
      id: string;
      customer_id: string;
      contact_date: string;
      message: string;
      result: string;
    };
    indexes: { 'by-customer': string };
  };
  caixa_operations: {
    key: string;
    value: {
      id: string;
      type: 'suprimento' | 'sangria';
      amount: number;
      description: string;
      created_at: string;
      created_by?: string;
    };
    indexes: { 'by-date': string };
  };
}

// Inicializar o banco de dados
let dbPromise: Promise<IDBPDatabase<DeliveryCRMDB>>;
let dbInitialized = false;

async function initDatabase() {
  if (!dbPromise) {
    dbPromise = openDB<DeliveryCRMDB>('delivery-crm-db', 1, {
      upgrade(db) {
        // Criar stores e indexes

        // Customers
        if (!db.objectStoreNames.contains('customers')) {
          const customerStore = db.createObjectStore('customers', { keyPath: 'id' });
          customerStore.createIndex('by-phone', 'phone', { unique: true });
        }

        // Addresses
        if (!db.objectStoreNames.contains('addresses')) {
          const addressStore = db.createObjectStore('addresses', { keyPath: 'id' });
          addressStore.createIndex('by-customer', 'customer_id', { unique: false });
        }

        // Deliverers
        if (!db.objectStoreNames.contains('deliverers')) {
          db.createObjectStore('deliverers', { keyPath: 'id' });
        }

        // Payment Methods
        if (!db.objectStoreNames.contains('payment_methods')) {
          const paymentMethodStore = db.createObjectStore('payment_methods', { keyPath: 'id' });
          paymentMethodStore.createIndex('by-code', 'code', { unique: true });
        }

        // Orders
        if (!db.objectStoreNames.contains('orders')) {
          const orderStore = db.createObjectStore('orders', { keyPath: 'id' });
          orderStore.createIndex('by-customer', 'customer_id', { unique: false });
          orderStore.createIndex('by-date', 'created_at', { unique: false });
        }

        // Reactivation Logs
        if (!db.objectStoreNames.contains('reactivation_logs')) {
          const reactivationLogStore = db.createObjectStore('reactivation_logs', { keyPath: 'id' });
          reactivationLogStore.createIndex('by-customer', 'customer_id', { unique: false });
        }

        // Caixa Operations
        if (!db.objectStoreNames.contains('caixa_operations')) {
          const caixaOperationStore = db.createObjectStore('caixa_operations', { keyPath: 'id' });
          caixaOperationStore.createIndex('by-date', 'created_at', { unique: false });
        }
      }
    });
  }

  // Aguardar a inicialização do banco de dados
  const db = await dbPromise;

  // Inserir métodos de pagamento padrão se ainda não foram inseridos
  if (!dbInitialized) {
    const count = await db.count('payment_methods');

    if (count === 0) {
      const paymentMethods = [
        {
          id: uuidv4(),
          name: 'Dinheiro',
          code: 'cash',
          default_paid: false,
          is_active: true,
          created_at: new Date().toISOString()
        },
        {
          id: uuidv4(),
          name: 'Cartão',
          code: 'card',
          default_paid: true,
          is_active: true,
          created_at: new Date().toISOString()
        },
        {
          id: uuidv4(),
          name: 'PIX',
          code: 'pix',
          default_paid: true,
          is_active: true,
          created_at: new Date().toISOString()
        },
        {
          id: uuidv4(),
          name: 'Venda do Site',
          code: 'venda_do_site',
          default_paid: true,
          is_active: true,
          created_at: new Date().toISOString()
        }
      ];

      const tx = db.transaction('payment_methods', 'readwrite');
      for (const method of paymentMethods) {
        await tx.store.put(method);
      }
      await tx.done;
    }

    dbInitialized = true;
  }

  return db;
}

// Inicializar o banco de dados
initDatabase().catch(error => {
  console.error('Erro ao inicializar o banco de dados:', error);
});

// Exportar funções de acesso ao banco de dados
export const dbService = {
  // Funções para clientes
  customers: {
    findByPhone: async (phone: string) => {
      const db = await initDatabase();
      return db.getFromIndex('customers', 'by-phone', phone);
    },
    create: async (customer: { name: string; phone: string }) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const newCustomer = {
        id,
        name: customer.name,
        phone: customer.phone,
        created_at: now,
        last_order_at: null
      };
      await db.put('customers', newCustomer);
      return newCustomer;
    },
    update: async (id: string, data: Partial<{ name: string; phone: string; last_order_at: string }>) => {
      const db = await initDatabase();
      const customer = await db.get('customers', id);
      if (!customer) return null;

      const updatedCustomer = { ...customer, ...data };
      await db.put('customers', updatedCustomer);
      return updatedCustomer;
    },
    getInactive: async (daysInactive: number) => {
      const db = await initDatabase();
      const date = new Date();
      date.setDate(date.getDate() - daysInactive);
      const dateStr = date.toISOString();

      const customers = await db.getAll('customers');
      return customers.filter(customer =>
        !customer.last_order_at || customer.last_order_at < dateStr
      ).sort((a, b) => {
        if (!a.last_order_at) return -1;
        if (!b.last_order_at) return 1;
        return a.last_order_at.localeCompare(b.last_order_at);
      });
    }
  },

  // Funções para endereços
  addresses: {
    findByCustomerId: async (customerId: string) => {
      const db = await initDatabase();
      return db.getAllFromIndex('addresses', 'by-customer', customerId);
    },
    create: async (address: {
      customer_id: string;
      street: string;
      number: string;
      complement?: string;
      neighborhood: string;
      city: string;
      is_default?: boolean;
    }) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const newAddress = {
        id,
        customer_id: address.customer_id,
        street: address.street,
        number: address.number,
        complement: address.complement || null,
        neighborhood: address.neighborhood,
        city: address.city,
        is_default: address.is_default || false,
        created_at: now
      };
      await db.put('addresses', newAddress);
      return newAddress;
    }
  },

  // Funções para entregadores
  deliverers: {
    getAll: async () => {
      const db = await initDatabase();
      const deliverers = await db.getAll('deliverers');
      return deliverers.sort((a, b) => a.name.localeCompare(b.name));
    },
    getActive: async () => {
      const db = await initDatabase();
      const deliverers = await db.getAll('deliverers');
      return deliverers
        .filter(d => d.is_active)
        .sort((a, b) => a.name.localeCompare(b.name));
    },
    create: async (name: string) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const newDeliverer = {
        id,
        name,
        is_active: true,
        created_at: now
      };
      await db.put('deliverers', newDeliverer);
      return newDeliverer;
    },
    toggleActive: async (id: string) => {
      const db = await initDatabase();
      const deliverer = await db.get('deliverers', id);
      if (!deliverer) return null;

      const updatedDeliverer = {
        ...deliverer,
        is_active: !deliverer.is_active
      };
      await db.put('deliverers', updatedDeliverer);
      return updatedDeliverer;
    }
  },

  // Funções para métodos de pagamento
  paymentMethods: {
    getAll: async () => {
      const db = await initDatabase();
      const methods = await db.getAll('payment_methods');
      return methods.sort((a, b) => a.name.localeCompare(b.name));
    },
    create: async (method: { name: string; code: string; default_paid: boolean }) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const newMethod = {
        id,
        name: method.name,
        code: method.code,
        default_paid: method.default_paid,
        is_active: true,
        created_at: now
      };
      await db.put('payment_methods', newMethod);
      return newMethod;
    }
  },

  // Funções para pedidos
  orders: {
    create: async (order: {
      customer_id: string;
      customer_name: string;
      customer_phone: string;
      address: any;
      products_total: number;
      delivery_fee: number;
      payment_method: string;
      payment_amount?: number;
      change_amount?: number;
      is_paid: boolean;
      status: string;
      deliverer_id?: string;
    }) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const total_amount = order.products_total + order.delivery_fee;

      console.log('Criando pedido com os dados:', {
        id,
        customer_id: order.customer_id,
        customer_name: order.customer_name,
        customer_phone: order.customer_phone,
        address: order.address,
        payment_method: order.payment_method
      });

      // Verificar se o endereço é válido
      if (!order.address) {
        console.error('Endereço não fornecido para o pedido');
      } else if (typeof order.address === 'object') {
        console.log('Endereço do pedido:', JSON.stringify(order.address));
      }

      // Buscar o nome do método de pagamento
      console.log('Buscando método de pagamento:', order.payment_method);
      let payment_method_name = null;
      try {
        const paymentMethod = await db.getFromIndex('payment_methods', 'by-code', order.payment_method);
        payment_method_name = paymentMethod ? paymentMethod.name : null;
        console.log('Método de pagamento encontrado:', paymentMethod);
      } catch (error) {
        console.error('Erro ao buscar método de pagamento:', error);
      }

      // Preparar o objeto do pedido
      const newOrder = {
        id,
        customer_id: order.customer_id,
        customer_name: order.customer_name,
        customer_phone: order.customer_phone,
        address: order.address ? JSON.stringify(order.address) : null, // Converter para string
        products_total: order.products_total,
        delivery_fee: order.delivery_fee,
        total_amount,
        payment_method: order.payment_method,
        payment_method_name,
        payment_amount: order.payment_amount || null,
        change_amount: order.change_amount || null,
        is_paid: order.is_paid,
        status: order.status,
        deliverer_id: order.deliverer_id || null,
        created_at: now
      };

      await db.put('orders', newOrder);

      // Atualizar a data do último pedido do cliente
      console.log('Atualizando data do último pedido para o cliente:', order.customer_id);
      if (order.customer_id && order.customer_id.trim() !== '') {
        try {
          const customer = await db.get('customers', order.customer_id);
          if (customer) {
            customer.last_order_at = now;
            await db.put('customers', customer);
            console.log('Data do último pedido atualizada com sucesso para o cliente:', order.customer_id);
          } else {
            console.warn('Cliente não encontrado para atualização de data do último pedido:', order.customer_id);
          }
        } catch (error) {
          console.error('Erro ao atualizar data do último pedido:', error);
          // Não propagar o erro para não interromper a criação do pedido
        }
      } else {
        console.warn('ID do cliente não fornecido ou inválido para atualização de data do último pedido');
      }

      return newOrder;
    },
    getByDateRange: async (startDate: string, endDate: string) => {
      const db = await initDatabase();
      const orders = await db.getAll('orders');

      return orders
        .filter(order => order.created_at >= startDate && order.created_at < endDate)
        .sort((a, b) => b.created_at.localeCompare(a.created_at));
    },
    updateStatus: async (id: string, status: string, additionalData: any = {}) => {
      const db = await initDatabase();
      const order = await db.get('orders', id);
      if (!order) return null;

      const updatedOrder = {
        ...order,
        status,
        ...additionalData
      };

      await db.put('orders', updatedOrder);
      return updatedOrder;
    }
  },

  // Funções para logs de reativação
  reactivationLogs: {
    create: async (log: { customer_id: string; message: string; result: string }) => {
      const db = await initDatabase();
      const id = uuidv4();
      const now = new Date().toISOString();
      const newLog = {
        id,
        customer_id: log.customer_id,
        contact_date: now,
        message: log.message,
        result: log.result
      };
      await db.put('reactivation_logs', newLog);
      return newLog;
    }
  },

  // Funções para operações de caixa
  caixaOperations: {
    getAll: async () => {
      try {
        const db = await initDatabase();
        const operations = await db.getAll('caixa_operations');
        return operations.sort((a, b) => b.created_at.localeCompare(a.created_at));
      } catch (error) {
        console.error('Erro ao buscar operações de caixa:', error);
        throw { code: '42P01', message: 'Tabela não existe' };
      }
    },
    create: async (operation: { type: 'suprimento' | 'sangria'; amount: number; description: string }) => {
      try {
        const db = await initDatabase();
        const id = uuidv4();
        const now = new Date().toISOString();
        const newOperation = {
          id,
          type: operation.type,
          amount: operation.amount,
          description: operation.description,
          created_at: now
        };
        await db.put('caixa_operations', newOperation);
        return newOperation;
      } catch (error) {
        console.error('Erro ao criar operação de caixa:', error);
        throw { code: '42P01', message: 'Tabela não existe' };
      }
    }
  }
};

export default dbService;
