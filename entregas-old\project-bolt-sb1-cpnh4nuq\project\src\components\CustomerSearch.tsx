import React, { useState } from 'react';
import { Search, Plus, Printer } from 'lucide-react';
import InputMask from 'react-input-mask';
import { supabase } from '../lib/supabase';
import type { Customer, Address, InputEvent, TextAreaEvent, SelectEvent } from '../types';

interface OrderDetails {
  products_total: number;
  delivery_fee: number;
  payment_method: 'pix' | 'card' | 'cash';
  notes: string;
}

interface ReceiptData {
  customer: Customer;
  address: Address;
  orderDetails: OrderDetails & { total: number };
}

const CustomerSearch: React.FC = () => {
  const [phone, setPhone] = useState('');
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [newCustomer, setNewCustomer] = useState({ name: '', phone: '' });
  const [address, setAddress] = useState<Partial<Address>>({
    street: '',
    number: '',
    neighborhood: '',
    city: '',
  });
  const [orderDetails, setOrderDetails] = useState<OrderDetails>({
    products_total: 0,
    delivery_fee: 0,
    payment_method: 'pix',
    notes: '',
  });
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const formattedPhone = phone.replace(/\D/g, '');
      const { data: customers, error } = await supabase
        .from('customers')
        .select('*')
        .eq('phone', formattedPhone);

      if (error) throw error;
      const customerData = customers?.[0] || null;
      setCustomer(customerData);
      if (customerData) {
        setNewCustomer({ name: customerData.name, phone: formattedPhone });
      } else {
        setNewCustomer({ name: '', phone: formattedPhone });
      }
    } catch (error) {
      console.error('Error searching customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = async () => {
    if (!newCustomer.name || !newCustomer.phone) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('customers')
        .upsert({
          phone: newCustomer.phone,
          name: newCustomer.name,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      setCustomer(data);
    } catch (error) {
      console.error('Error creating customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = async () => {
    if (!customer) return;
    
    setLoading(true);
    try {
      // First create address if needed
      const { data: addressData, error: addressError } = await supabase
        .from('addresses')
        .insert({
          customer_id: customer.id,
          ...address,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (addressError) throw addressError;

      // Then create order
      const total = orderDetails.products_total + orderDetails.delivery_fee;
      const { error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_id: customer.id,
          address_id: addressData.id,
          products_total: orderDetails.products_total,
          delivery_fee: orderDetails.delivery_fee,
          total_amount: total,
          payment_method: orderDetails.payment_method,
          status: 'pending',
          notes: orderDetails.notes,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (orderError) throw orderError;

      // Reset form
      setAddress({
        street: '',
        number: '',
        neighborhood: '',
        city: '',
      });
      setOrderDetails({
        products_total: 0,
        delivery_fee: 0,
        payment_method: 'pix',
        notes: '',
      });
      
      // Print receipt
      printReceipt({
        customer: customer,
        address: addressData,
        orderDetails: {
          ...orderDetails,
          total,
        },
      });
    } catch (error) {
      console.error('Error creating order:', error);
    } finally {
      setLoading(false);
    }
  };

  const printReceipt = (data: ReceiptData) => {
    const receiptWindow = window.open('', '', 'width=300,height=600');
    if (!receiptWindow) return;

    const style = `
      <style>
        body { font-family: monospace; width: 80mm; margin: 0; padding: 10px; }
        .header { text-align: center; margin-bottom: 20px; }
        .divider { border-top: 1px dashed #000; margin: 10px 0; }
        .total { font-size: 1.2em; font-weight: bold; }
      </style>
    `;

    const content = `
      <div class="header">
        <h2>COMPROVANTE DE ENTREGA</h2>
        <p>${new Date().toLocaleString()}</p>
      </div>
      <div class="customer">
        <p><strong>Cliente:</strong> ${data.customer.name}</p>
        <p><strong>Telefone:</strong> ${data.customer.phone}</p>
      </div>
      <div class="divider"></div>
      <div class="address">
        <p><strong>Endereço de Entrega:</strong></p>
        <p>${data.address.street}, ${data.address.number}</p>
        <p>${data.address.neighborhood} - ${data.address.city}</p>
      </div>
      <div class="divider"></div>
      <div class="order">
        <p><strong>Valor Produtos:</strong> R$ ${data.orderDetails.products_total.toFixed(2)}</p>
        <p><strong>Taxa de Entrega:</strong> R$ ${data.orderDetails.delivery_fee.toFixed(2)}</p>
        <p><strong>Forma de Pagamento:</strong> ${data.orderDetails.payment_method.toUpperCase()}</p>
        ${data.orderDetails.notes ? `<p><strong>Observações:</strong> ${data.orderDetails.notes}</p>` : ''}
      </div>
      <div class="divider"></div>
      <div class="total">
        <p>TOTAL: R$ ${data.orderDetails.total.toFixed(2)}</p>
      </div>
    `;

    receiptWindow.document.write(`<html><head>${style}</head><body>${content}</body></html>`);
    receiptWindow.document.close();
    receiptWindow.print();
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <InputMask
              mask="+55 (99) 99999-9999"
              value={phone}
              onChange={(e: InputEvent) => setPhone(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Digite o telefone do cliente"
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={loading}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <Search className="h-5 w-5" />
          </button>
        </div>
      </div>

      {(phone && !customer || customer) && (
        <div className="bg-white shadow-sm rounded-lg p-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Dados do Cliente</h3>
              <div className="space-y-4">
                <input
                  type="text"
                  value={newCustomer.name}
                  onChange={(e: InputEvent) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                  placeholder="Nome do cliente"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md"
                />
                {!customer && (
                  <button
                    onClick={handleCreateCustomer}
                    disabled={loading || !newCustomer.name}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 inline mr-2" />
                    Criar Novo Cliente
                  </button>
                )}
              </div>
            </div>

            {customer && (
              <>
                <div>
                  <h3 className="text-lg font-medium mb-4">Endereço de Entrega</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <input
                      type="text"
                      value={address.street}
                      onChange={(e: InputEvent) => setAddress({ ...address, street: e.target.value })}
                      placeholder="Rua"
                      className="col-span-2 px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      value={address.number}
                      onChange={(e: InputEvent) => setAddress({ ...address, number: e.target.value })}
                      placeholder="Número"
                      className="px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      value={address.neighborhood}
                      onChange={(e: InputEvent) => setAddress({ ...address, neighborhood: e.target.value })}
                      placeholder="Bairro"
                      className="px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      value={address.city}
                      onChange={(e: InputEvent) => setAddress({ ...address, city: e.target.value })}
                      placeholder="Cidade"
                      className="col-span-2 px-4 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Detalhes do Pedido</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Valor Produtos
                        </label>
                        <input
                          type="number"
                          value={orderDetails.products_total}
                          onChange={(e: InputEvent) => setOrderDetails({ ...orderDetails, products_total: parseFloat(e.target.value) || 0 })}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md"
                          step="0.01"
                          min="0"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Taxa de Entrega
                        </label>
                        <input
                          type="number"
                          value={orderDetails.delivery_fee}
                          onChange={(e: InputEvent) => setOrderDetails({ ...orderDetails, delivery_fee: parseFloat(e.target.value) || 0 })}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md"
                          step="0.01"
                          min="0"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Forma de Pagamento
                      </label>
                      <select
                        value={orderDetails.payment_method}
                        onChange={(e: SelectEvent) => setOrderDetails({ ...orderDetails, payment_method: e.target.value as any })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="pix">PIX</option>
                        <option value="card">Cartão</option>
                        <option value="cash">Dinheiro</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Observações
                      </label>
                      <textarea
                        value={orderDetails.notes}
                        onChange={(e: TextAreaEvent) => setOrderDetails({ ...orderDetails, notes: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md"
                        rows={3}
                      />
                    </div>
                    <button
                      onClick={handleCreateOrder}
                      disabled={loading || !address.street || !address.number || !address.neighborhood || !address.city}
                      className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                    >
                      <Printer className="h-4 w-4 inline mr-2" />
                      Finalizar e Imprimir
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerSearch;