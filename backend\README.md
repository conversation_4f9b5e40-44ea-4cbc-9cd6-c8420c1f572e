# Backend Local com SQLite

## Configuração

1. Instale as dependências:
```bash
npm install
```

2. Inicie o servidor:
```bash
npm start
```

Ou em modo de desenvolvimento:
```bash
npm run dev
```

## Endpoints disponíveis

- `GET /api/deliverers` - Lista todos os entregadores
- `GET /api/orders` - Lista todos os pedidos
- `POST /api/orders` - Cria novo pedido
- `PUT /api/orders/:id` - Atualiza pedido

O banco de dados SQLite será criado automaticamente em `database/local.db`
