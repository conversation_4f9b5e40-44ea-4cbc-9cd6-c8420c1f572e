{"name": "delivery-crm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.3", "date-fns": "^3.3.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-input-mask": "^3.0.0-alpha.2", "react-router-dom": "^7.4.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-input-mask": "^3.0.5", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "dotenv": "^16.4.7", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.2.2"}}