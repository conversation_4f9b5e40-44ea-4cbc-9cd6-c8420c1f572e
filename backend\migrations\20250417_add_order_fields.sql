-- Adiciona campos relevantes à tabela de pedidos (orders)
ALTER TABLE orders ADD COLUMN address_id INTEGER;
ALTER TABLE orders ADD COLUMN products_total REAL DEFAULT 0;
ALTER TABLE orders ADD COLUMN delivery_fee REAL DEFAULT 0;
ALTER TABLE orders ADD COLUMN payment_method TEXT;
ALTER TABLE orders ADD COLUMN payment_amount REAL;
ALTER TABLE orders ADD COLUMN change_amount REAL;
ALTER TABLE orders ADD COLUMN is_paid INTEGER DEFAULT 0;

-- Opcional: cria relação com addresses
-- ALTER TABLE orders ADD FOREIGN KEY(address_id) REFERENCES addresses(id);
