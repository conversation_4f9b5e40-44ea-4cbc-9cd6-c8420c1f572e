import React, { useState, useEffect } from 'react';
import { fetchOrders, updateOrder, fetchAddresses } from '../lib/api';
import type { Order, Address } from '../lib/api';
import { format, startOfDay, endOfDay, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Printer, ChevronLeft, ChevronRight } from 'lucide-react';
import ThermalReceipt from './ThermalReceipt';

const statusConfig = {
  generated: {
    label: 'Gerado',
    color: 'bg-yellow-100 text-yellow-800',
    hoverColor: 'hover:bg-yellow-200'
  },
  sent: {
    label: 'Enviado',
    color: 'bg-blue-100 text-blue-800',
    hoverColor: 'hover:bg-blue-200'
  },
  returned: {
    label: 'Retornado',
    color: 'bg-green-100 text-green-800',
    hoverColor: 'hover:bg-green-200'
  },
  cancelled: {
    label: 'Desistência',
    color: 'bg-red-200 text-red-900',
    hoverColor: 'hover:bg-red-300'
  }
};

// Definições locais de tipos para status de pedido
type OrderStatus = 'generated' | 'sent' | 'returned' | 'cancelled';
const statusOrder: OrderStatus[] = ['generated', 'sent', 'returned', 'cancelled'];

export default function OrderManagement() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [printOrder, setPrintOrder] = useState<Order | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());

  const loadOrders = async () => {
    setLoading(true);
    try {
      const start = startOfDay(selectedDate);
      const end = endOfDay(selectedDate);

      const all = await fetchOrders();
      const filtered = all
        .filter(o => {
          const d = new Date(o.created_at);
          return d >= start && d <= end;
        })
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      // Anexa o endereço específico do pedido (usando address_id)
      const withAddresses = await Promise.all(filtered.map(async order => {
        let addresses: Address[] = [];
        if (order.customer_id) addresses = await fetchAddresses(order.customer_id);

        // Busca o endereço específico do pedido usando address_id
        const address: Address = addresses.find(addr => addr.id === order.address_id) || addresses[0] || {
          id: '0',
          customer_id: order.customer_id || '0',
          address: '',
          complement: '',
          neighborhood: '',
          city: '',
          is_default: false,
          created_at: ''
        };
        return { ...order, address };
      }));
      setOrders(withAddresses);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrders();
  }, [selectedDate]);

  const handleStatusChange = async (order: Order, newStatus: OrderStatus) => {
    // Não permitir mudança de status para pedidos de outros dias
    if (!isToday(new Date(order.created_at))) {
      alert('Só é possível alterar status de pedidos do dia!');
      return;
    }
    // Se for "returned" e não for pagamento em dinheiro, não fazer nada
    if (newStatus === 'returned' && order.payment_method !== 'cash') return;

    try {
      const updated = await updateOrder(order.id, { status: newStatus });
      // Atualizar a lista localmente
      setOrders(orders.map(o => 
        o.id === order.id ? { ...o, status: newStatus } : o
      ));
      setSelectedOrder(null); // Não dispara impressão
    } catch (error: any) {
      console.error('Error updating order status:', error);
      alert('Erro ao atualizar status do pedido: ' + (error?.message || error));
    }
  };

  const handlePrint = (order: Order) => {
    setPrintOrder(order);
  };

  const changeDate = (days: number) => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + days);
    setSelectedDate(newDate);
  };

  if (printOrder) {
    return <ThermalReceipt order={printOrder} onClose={() => setPrintOrder(null)} />;
  }

  return (
    <div className="space-y-4">
      <div className="bg-white shadow-sm rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Gestão de Pedidos</h2>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => changeDate(-1)}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <span className="font-medium">
              {format(selectedDate, "dd 'de' MMMM", { locale: ptBR })}
            </span>
            <button
              onClick={() => changeDate(1)}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        {loading ? (
          <div className="text-center py-4">Carregando pedidos...</div>
        ) : (
          <div className="space-y-2">
            {orders.map((order) => {
              const isCurrentDay = isToday(new Date(order.created_at));
              const isCancelled = order.status === 'cancelled';
              const isCompleted = order.status === 'sent' || order.status === 'returned';

              return (
                <div 
                  key={order.id} 
                  className={`
                    border rounded-lg p-2 
                    ${isCurrentDay ? 'bg-white' : 'bg-gray-50'}
                    ${isCancelled ? 'opacity-60' : ''}
                    ${isCompleted ? 'border-green-500 border-2' : ''}
                    ${isCancelled ? 'border-red-500 border-2' : ''}
                  `}
                >
                  <div className={`flex justify-between items-start gap-2 ${isCancelled ? 'opacity-50' : ''}`}>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">#{order.id.toString().slice(0, 8)} - {order.customer_name}</p>
                          <p className="text-sm text-gray-500 mt-0.5">
                            {order.customer_phone?.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3')}
                          </p>
                          <p className="text-sm text-gray-600 mt-0.5">
                            {order.address.address} - {order.address.neighborhood}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {order.payment_method === 'venda_do_site'
                              ? 'Venda Site'
                              : `R$ ${(order.products_total + order.delivery_fee).toFixed(2)}`}
                          </p>
                          <p className="text-sm text-gray-500 mt-0.5">
                            {order.payment_method}
                          </p>
                          {order.payment_method === 'cash' && (
                            <>
                              <p className="text-sm text-gray-500 mt-0.5">
                                Entrega: R$ {order.delivery_fee?.toFixed(2)}
                              </p>
                              <p className="text-sm text-gray-500 mt-0.5">
                                Motoboy trazer: R$ {order.payment_amount?.toFixed(2)}
                              </p>
                            </>
                          )}
                          <p className="text-sm text-gray-500 mt-0.5">
                            {format(new Date(order.created_at), 'HH:mm')}
                          </p>
                          <button
                            onClick={() => setSelectedOrder(order)}
                            className="mt-1 p-1.5 text-indigo-700 rounded-md hover:bg-indigo-50"
                          >
                            <Printer className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="mt-2 flex gap-1">
                        {statusOrder.map((status) => {
                          const isDisabled = !isCurrentDay || 
                            (status === 'returned' && order.payment_method !== 'cash') ||
                            (isCancelled && status !== 'cancelled');
                          const config = statusConfig[status];
                          return (
                            <button
                              key={status}
                              className={`ml-2 px-2 py-1 rounded ${config.color} ${config.hoverColor}`}
                              onClick={() => handleStatusChange(order, status as OrderStatus)}
                              title={
                                !isCurrentDay ? 'Pedidos de outros dias não podem ter o status alterado' :
                                status === 'returned' && order.payment_method !== 'cash' ? 'Apenas pedidos em dinheiro podem ser retornados' :
                                isCancelled && status !== 'cancelled' ? 'Pedidos cancelados não podem ter seu status alterado' :
                                ''
                              }
                              disabled={isDisabled}
                            >
                              {config.label}
                            </button>
                          );
                        })}
                        <button
                          className="ml-2 px-2 py-1 rounded bg-gray-200 hover:bg-gray-300"
                          title="Imprimir recibo"
                          onClick={() => handlePrint(order)}
                        >
                          <Printer size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            {orders.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                Nenhum pedido encontrado
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
