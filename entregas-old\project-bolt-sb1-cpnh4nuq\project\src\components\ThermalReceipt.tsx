import { useEffect } from 'react';
import type { Order } from '../types';

interface ThermalReceiptProps {
  order: Order;
  onClose: () => void;
}

export default function ThermalReceipt({ order, onClose }: ThermalReceiptProps) {
  useEffect(() => {
    // Atraso pequeno para garantir que o DOM foi renderizado
    const timeoutId = setTimeout(() => {
      window.print();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []); // Executa apenas uma vez na montagem

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return 'R$ 0,00';
    // Arredonda para 2 casas decimais e converte para string com vírgula
    return `R$ ${(Math.round(value * 100) / 100).toFixed(2).replace('.', ',')}`;
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-4">
          <div className="receipt">
            <style>
              {`
                @media print {
                  body * {
                    visibility: hidden;
                  }
                  .receipt, .receipt * {
                    visibility: visible;
                  }
                  .receipt {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 80mm;
                    padding: 5mm;
                  }
                  .no-print {
                    display: none;
                  }
                  /* Alto contraste para impressão térmica */
                  .receipt * {
                    color: black !important;
                    font-weight: bold !important;
                    text-shadow: none !important;
                  }
                  .receipt img {
                    filter: contrast(200%) brightness(0) !important;
                  }
                  /* Garantir que o valor a pagar fique em negrito na impressão */
                  .receipt p {
                    color: black !important;
                    font-weight: bold !important;
                    margin: 0.3em 0 !important;
                  }
                  .receipt-divider {
                    margin: 0.5em 0 !important;
                    border-top: 1px solid black !important;
                  }
                }
                .receipt {
                  font-family: monospace;
                  width: 80mm;
                  margin: 0 auto;
                  padding: 5mm;
                  text-align: center;
                }
                .receipt-header {
                  text-align: center;
                  margin-bottom: 0.5em;
                }
                .receipt-logo {
                  height: 32px;
                  width: auto;
                  margin: 0 auto 0.5em;
                  display: block;
                  /* Alto contraste na preview também */
                  filter: contrast(200%) brightness(0);
                }
                .receipt-divider {
                  border-top: 1px solid black;
                  margin: 0.5em 0;
                }
                .receipt p {
                  margin: 0.3em 0;
                }
              `}
            </style>

            <div className="receipt-header">
              <img
                src="/recibo.png"
                alt="Delivery CRM"
                className="receipt-logo"
                onError={(e) => {
                  const img = e.target as HTMLImageElement;
                  img.onerror = null; // Previne loop infinito
                  img.src = '/receipt-logo.svg';
                }}
              />
              <div>
                PEDIDO #{order.id.slice(0, 8)}
                <br />
                {new Date(order.created_at).toLocaleString()}
              </div>
            </div>

            <div className="receipt-divider" />

            <div style={{ textAlign: 'left' }}>
              <p><strong>Cliente:</strong> {order.customer_name}</p>
              <p><strong>Telefone:</strong> {order.customer_phone}</p>
              <p>
                <strong>Endereço:</strong> {order.address.street}, {order.address.number}
                {order.address.complement && ` - ${order.address.complement}`}
                <br />
                {order.address.neighborhood} - {order.address.city}
              </p>
            </div>

            <div className="receipt-divider" />

            <div style={{ textAlign: 'right', minHeight: '5em' }}>
              {order.payment_method === 'venda_do_site' ? (
                <p>Obrigado por comprar em newlookcosmeticos.com.br</p>
              ) : (
                <>
                  <p><strong>Produtos:</strong> {formatCurrency(order.products_total)}</p>
                  <p><strong>Entrega:</strong> {formatCurrency(order.delivery_fee)}</p>
                  <p><strong>Total:</strong> {formatCurrency(order.products_total + order.delivery_fee)}</p>
                  <p><strong>Forma de pagamento:</strong> {
                    order.payment_method === 'pix' ? 'PIX' :
                    order.payment_method === 'card' ? 'Cartão' :
                    'Dinheiro'
                  }</p>
                  {order.payment_method === 'cash' && !order.is_paid && order.change_amount && order.change_amount > 0 && (
                    <p><strong>Troco:</strong> {formatCurrency(order.change_amount)}</p>
                  )}
                </>
              )}
            </div>

            <div className="receipt-divider" />

            {/* Status de Pagamento em Destaque */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              border: '2px solid black',
              padding: '0.5em',
              margin: '0.5em 0'
            }}>
              {order.is_paid ? (
                <strong style={{ 
                  fontSize: '1.5em',
                  fontWeight: 'bold',
                  width: '100%',
                  textAlign: 'center',
                  letterSpacing: '0.1em'
                }}>
                  * PAGO *
                </strong>
              ) : (
                <>
                  <strong style={{ fontSize: '1.1em' }}>VALOR A PAGAR:</strong>
                  <span style={{ fontSize: '1.3em', fontWeight: 'bold' }}>
                    {formatCurrency(
                      order.payment_method === 'cash' 
                        ? order.payment_amount || (order.products_total + order.delivery_fee)
                        : order.products_total + order.delivery_fee
                    )}
                  </span>
                </>
              )}
            </div>
          </div>

          <div className="mt-4 flex justify-end no-print">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
