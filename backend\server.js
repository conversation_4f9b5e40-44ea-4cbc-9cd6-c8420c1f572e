const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const caixaApi = require('./caixa_api');
const caixaCountApi = require('./caixa_count_api');

// Configuração inicial
const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/api/caixa', caixaApi);
app.use('/api/caixa-count', caixaCountApi);

// Conexão com o banco de dados
const db = new sqlite3.Database(path.join(__dirname, 'database/local.db'));

// Criar tabelas (se não existirem)
db.serialize(() => {
  // Migração: adicionar coluna phone em deliverers se não existir
  db.run("ALTER TABLE deliverers ADD COLUMN phone TEXT", (err) => {/* ignora se já existe */});
  db.run(`
    CREATE TABLE IF NOT EXISTS deliverers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      customer_name TEXT NOT NULL,
      customer_phone TEXT,
      deliverer_id INTEGER,
      status TEXT DEFAULT 'pending',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(deliverer_id) REFERENCES deliverers(id)
    );
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS payment_methods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      code TEXT UNIQUE NOT NULL,
      default_paid INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Clientes e endereços
  db.run(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT UNIQUE NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
  db.run(`
    CREATE TABLE IF NOT EXISTS addresses (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      customer_id INTEGER NOT NULL,
      street TEXT NOT NULL,
      number TEXT NOT NULL,
      neighborhood TEXT NOT NULL,
      city TEXT NOT NULL,
      complement TEXT,
      is_default INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(customer_id) REFERENCES customers(id)
    );
  `);
});

// Rotas básicas
app.get('/api/deliverers', (req, res) => {
  db.all('SELECT * FROM deliverers', [], (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    // Mapear para garantir compatibilidade
    const deliverers = rows.map(row => ({
      id: row.id,
      name: row.name,
      is_active: Boolean(row.is_active),
      created_at: row.created_at
    }));
    res.json(deliverers);
  });
});

// Rotas de entregadores (CRUD)
app.post('/api/deliverers', (req, res) => {
  const { name, phone, is_active } = req.body;
  db.run(
    'INSERT INTO deliverers (name, phone, is_active) VALUES (?, ?, ?)',
    [name, phone, is_active ? 1 : 0],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      db.get('SELECT * FROM deliverers WHERE id = ?', [this.lastID], (err, row) => {
        if (err) return res.status(500).json({ error: err.message });
        row.is_active = Boolean(row.is_active);
        res.status(201).json(row);
      });
    }
  );
});

app.put('/api/deliverers/:id', (req, res) => {
  const { id } = req.params;
  const { name, phone, is_active } = req.body;
  db.run(
    'UPDATE deliverers SET name = ?, phone = ?, is_active = ? WHERE id = ?',
    [name, phone, is_active ? 1 : 0, id],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      db.get('SELECT * FROM deliverers WHERE id = ?', [id], (err, row) => {
        if (err) return res.status(500).json({ error: err.message });
        row.is_active = Boolean(row.is_active);
        res.json(row);
      });
    }
  );
});

app.delete('/api/deliverers/:id', (req, res) => {
  const { id } = req.params;
  db.run('DELETE FROM deliverers WHERE id = ?', [id], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    res.status(204).send();
  });
});

// Rotas de métodos de pagamento
app.get('/api/payment-methods', (req, res) => {
  db.all('SELECT * FROM payment_methods', [], (err, rows) => {
    if (err) return res.status(500).json({ error: err.message });
    const methods = rows.map(row => ({ id: row.id, name: row.name, code: row.code, default_paid: Boolean(row.default_paid), is_active: Boolean(row.is_active), created_at: row.created_at }));
    res.json(methods);
  });
});

app.post('/api/payment-methods', (req, res) => {
  const { name, code, default_paid } = req.body;
  db.run('INSERT INTO payment_methods (name, code, default_paid) VALUES (?, ?, ?)', [name, code, default_paid ? 1 : 0], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    db.get('SELECT * FROM payment_methods WHERE id = ?', [this.lastID], (e, row) => {
      if (e) return res.status(500).json({ error: e.message });
      row.default_paid = Boolean(row.default_paid);
      row.is_active = Boolean(row.is_active);
      res.status(201).json(row);
    });
  });
});

app.put('/api/payment-methods/:id', (req, res) => {
  const { id } = req.params;
  const { name, code, default_paid, is_active } = req.body;
  db.run('UPDATE payment_methods SET name = ?, code = ?, default_paid = ?, is_active = ? WHERE id = ?', [name, code, default_paid ? 1 : 0, is_active ? 1 : 0, id], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    db.get('SELECT * FROM payment_methods WHERE id = ?', [id], (e, row) => {
      if (e) return res.status(500).json({ error: e.message });
      row.default_paid = Boolean(row.default_paid);
      row.is_active = Boolean(row.is_active);
      res.json(row);
    });
  });
});

app.delete('/api/payment-methods/:id', (req, res) => {
  const { id } = req.params;
  db.run('DELETE FROM payment_methods WHERE id = ?', [id], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    res.status(204).send();
  });
});

// Rotas de pedidos (CRUD)
app.get('/api/orders', (req, res) => {
  // Buscar pedidos com joins para address, deliverer e método de pagamento
  const sql = `
    SELECT o.*, 
      a.id as address_id, a.street, a.number, a.complement, a.neighborhood, a.city, a.is_default,
      d.id as deliverer_id, d.name as deliverer_name,
      pm.name as payment_method_name
    FROM orders o
    LEFT JOIN addresses a ON o.address_id = a.id
    LEFT JOIN deliverers d ON o.deliverer_id = d.id
    LEFT JOIN payment_methods pm ON o.payment_method = pm.code
    ORDER BY o.created_at DESC
  `;
  db.all(sql, [], (err, rows) => {
    if (err) return res.status(500).json({ error: err.message });
    // Montar o objeto address e outros campos aninhados para cada pedido
    const orders = rows.map(row => ({
      id: row.id,
      customer_id: row.customer_id,
      customer_name: row.customer_name,
      customer_phone: row.customer_phone,
      address: row.address_id ? {
        id: row.address_id,
        street: row.street,
        number: row.number,
        complement: row.complement,
        neighborhood: row.neighborhood,
        city: row.city,
        is_default: Boolean(row.is_default)
      } : null,
      products_total: row.products_total,
      delivery_fee: row.delivery_fee,
      payment_method: row.payment_method,
      payment_method_name: row.payment_method_name,
      payment_amount: row.payment_amount,
      change_amount: row.change_amount,
      is_paid: Boolean(row.is_paid),
      status: row.status,
      deliverer_id: row.deliverer_id,
      deliverer_name: row.deliverer_name,
      created_at: row.created_at
    }));
    res.json(orders);
  });
});

// Cadastrar pedido, sem falha em deliverer_id inválido
app.post('/api/orders', (req, res) => {
  const {
    customer_id,
    customer_name,
    customer_phone,
    deliverer_id,
    status,
    address_id,
    products_total,
    delivery_fee,
    payment_method,
    payment_amount,
    change_amount,
    is_paid
  } = req.body;
  const deliv = deliverer_id || null;
  db.run(
    `INSERT INTO orders (
      customer_id, customer_name, customer_phone, deliverer_id, status,
      address_id, products_total, delivery_fee, payment_method, payment_amount, change_amount, is_paid
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      customer_id || null,
      customer_name,
      customer_phone,
      deliv,
      status || 'pending',
      address_id || null,
      products_total || 0,
      delivery_fee || 0,
      payment_method || null,
      payment_amount || null,
      change_amount || null,
      is_paid ? 1 : 0
    ],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      // Buscar o pedido recém-criado com todos os joins
      const sql = `
        SELECT o.*, 
          a.id as address_id, a.street, a.number, a.complement, a.neighborhood, a.city, a.is_default,
          d.id as deliverer_id, d.name as deliverer_name,
          pm.name as payment_method_name
        FROM orders o
        LEFT JOIN addresses a ON o.address_id = a.id
        LEFT JOIN deliverers d ON o.deliverer_id = d.id
        LEFT JOIN payment_methods pm ON o.payment_method = pm.code
        WHERE o.id = ?
      `;
      db.get(sql, [this.lastID], (err2, row) => {
        if (err2) return res.status(500).json({ error: err2.message });
        if (!row) return res.status(500).json({ error: 'Order not found' });
        const order = {
          id: row.id,
          customer_id: row.customer_id,
          customer_name: row.customer_name,
          customer_phone: row.customer_phone,
          address: row.address_id ? {
            id: row.address_id,
            street: row.street,
            number: row.number,
            complement: row.complement,
            neighborhood: row.neighborhood,
            city: row.city,
            is_default: Boolean(row.is_default)
          } : null,
          products_total: row.products_total,
          delivery_fee: row.delivery_fee,
          payment_method: row.payment_method,
          payment_method_name: row.payment_method_name,
          payment_amount: row.payment_amount,
          change_amount: row.change_amount,
          is_paid: Boolean(row.is_paid),
          status: row.status,
          deliverer_id: row.deliverer_id,
          deliverer_name: row.deliverer_name,
          created_at: row.created_at
        };
        res.status(201).json(order);
      });
    }
  );
});

app.put('/api/orders/:id', (req, res) => {
  const { id } = req.params;
  const { status, deliverer_id } = req.body;
  db.run(
    'UPDATE orders SET status = ?, deliverer_id = ? WHERE id = ?',
    [status, deliverer_id, id],
    function(err) {
      if (err) {
        console.error('Error updating order:', err.message);
        return res.status(500).json({ error: err.message });
      }
      const sql = `
        SELECT o.*, 
          a.id AS address_id, a.street, a.number, a.complement, a.neighborhood, a.city, a.is_default,
          d.id AS deliverer_id, d.name AS deliverer_name,
          pm.name AS payment_method_name
        FROM orders o
        LEFT JOIN addresses a ON o.address_id = a.id
        LEFT JOIN deliverers d ON o.deliverer_id = d.id
        LEFT JOIN payment_methods pm ON o.payment_method = pm.code
        WHERE o.id = ?
      `;
      db.get(sql, [id], (err2, row) => {
        if (err2) {
          console.error('Error fetching updated order:', err2.message);
          return res.status(500).json({ error: err2.message });
        }
        const order = {
          id: row.id,
          customer_id: row.customer_id,
          customer_name: row.customer_name,
          customer_phone: row.customer_phone,
          address: row.address_id ? {
            id: row.address_id,
            street: row.street,
            number: row.number,
            complement: row.complement,
            neighborhood: row.neighborhood,
            city: row.city,
            is_default: Boolean(row.is_default)
          } : null,
          products_total: row.products_total,
          delivery_fee: row.delivery_fee,
          payment_method: row.payment_method,
          payment_method_name: row.payment_method_name,
          payment_amount: row.payment_amount,
          change_amount: row.change_amount,
          is_paid: Boolean(row.is_paid),
          status: row.status,
          deliverer_id: row.deliverer_id,
          deliverer_name: row.deliverer_name,
          created_at: row.created_at
        };
        res.json(order);
      });
    }
  );
});

// Rotas de clientes
// Cadastrar ou retornar cliente existente
app.post('/api/customers', (req, res) => {
  const { name, phone } = req.body;
  // Evita erro de UNIQUE, insere apenas se não existir
  db.run('INSERT OR IGNORE INTO customers (name, phone) VALUES (?, ?)', [name, phone], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    // Retorna o cliente pelo phone (novo ou existente)
    db.get('SELECT * FROM customers WHERE phone = ?', [phone], (err, row) => {
      if (err) return res.status(500).json({ error: err.message });
      res.status(row.id === this.lastID ? 201 : 200).json(row);
    });
  });
});

app.get('/api/customers', (req, res) => {
  const phone = req.query.phone;
  if (phone) {
    db.get('SELECT * FROM customers WHERE phone = ?', [phone], (err, row) => {
      if (err) return res.status(500).json({ error: err.message });
      if (!row) return res.status(404).send();
      res.json(row);
    });
  } else {
    db.all('SELECT * FROM customers', [], (err, rows) => {
      if (err) return res.status(500).json({ error: err.message });
      res.json(rows);
    });
  }
});

// Rotas de endereços
app.get('/api/addresses', (req, res) => {
  const customer_id = req.query.customer_id;
  if (!customer_id) return res.status(400).json({ error: 'customer_id required' });
  db.all('SELECT * FROM addresses WHERE customer_id = ?', [customer_id], (err, rows) => {
    if (err) return res.status(500).json({ error: err.message });
    // Mapear os campos para garantir compatibilidade
    const addresses = rows.map(row => ({
      id: row.id,
      customer_id: row.customer_id,
      street: row.street,
      number: row.number,
      complement: row.complement,
      neighborhood: row.neighborhood,
      city: row.city,
      is_default: Boolean(row.is_default),
      created_at: row.created_at
    }));
    res.json(addresses);
  });
});

app.post('/api/addresses', (req, res) => {
  const { customer_id, street, number, neighborhood, city, complement } = req.body;
  db.run(
    'INSERT INTO addresses (customer_id, street, number, neighborhood, city, complement) VALUES (?, ?, ?, ?, ?, ?)',
    [customer_id, street, number, neighborhood, city, complement || null],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      db.get('SELECT * FROM addresses WHERE id = ?', [this.lastID], (err, row) => {
        if (err) return res.status(500).json({ error: err.message });
        res.status(201).json(row);
      });
    }
  );
});

// Endpoint de constraints de orders
app.get('/api/order-constraints', (req, res) => {
  db.all("PRAGMA table_info('orders')", [], (err, rows) => {
    if (err) return res.status(500).json({ error: err.message });
    const constraints = rows.map(r => ({
      column_name: r.name,
      type: r.type,
      notnull: r.notnull,
      default_value: r.dflt_value,
      pk: r.pk
    }));
    res.json(constraints);
  });
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`Servidor rodando em http://localhost:${port}`);
});
