import React, { useState, ChangeEvent } from 'react';
import { Search, Plus, Printer } from 'lucide-react';
import type { Customer, Address } from '../lib/api';
import { fetchCustomerByPhone, createCustomer, createAddress, createOrder, fetchAddresses } from '../lib/api';

interface OrderDetails {
  products_total: number;
  delivery_fee: number;
  payment_method: 'pix' | 'card' | 'cash';
  notes: string;
}

interface ReceiptData {
  customer: Customer;
  address: Address;
  orderDetails: OrderDetails & { total: number };
}

const CustomerSearch: React.FC = () => {
  const [phone, setPhone] = useState('');
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [newCustomer, setNewCustomer] = useState({ name: '', phone: '' });
  const [address, setAddress] = useState<Partial<Address>>({
    address: '',
    complement: '',
    neighborhood: '',
    city: '',
  });
  const [orderDetails, setOrderDetails] = useState<OrderDetails>({
    products_total: 0,
    delivery_fee: 0,
    payment_method: 'pix',
    notes: '',
  });
  const [customerAddresses, setCustomerAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [addingNew, setAddingNew] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const formattedPhone = phone.replace(/\D/g, '');
      const existing = await fetchCustomerByPhone(formattedPhone);
      setCustomer(existing);
      if (existing) {
        const addrs = await fetchAddresses(existing.id);
        setCustomerAddresses(addrs);
        setSelectedAddress(addrs[0] || null);
        setAddingNew(addrs.length === 0);
      }
      if (existing) {
        setNewCustomer({ name: existing.name, phone: formattedPhone });
      } else {
        setNewCustomer({ name: '', phone: formattedPhone });
      }
    } catch (error) {
      console.error('Error searching customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = async () => {
    if (!newCustomer.name.trim()) {
      alert('Por favor, informe o nome do cliente antes de cadastrar.');
      return;
    }
    if (!newCustomer.phone) return;
    setLoading(true);
    try {
      const customerData = await createCustomer({ name: newCustomer.name.trim(), phone: newCustomer.phone });
      setCustomer(customerData);
      setCustomerAddresses([]);
      setSelectedAddress(null);
      setAddingNew(true);
    } catch (error) {
      console.error('Error creating customer:', error);
    } finally {
      setLoading(false);
    }
  };


  const handleCreateOrder = async () => {
    if (!customer) return;
    
    setLoading(true);
    try {
      const addressData = selectedAddress
        ? selectedAddress
        : await createAddress({
            customer_id: customer.id,
            address: address.address!,
            complement: address.complement,
            neighborhood: address.neighborhood!,
            city: address.city!,
          });

      await createOrder({ customer_name: customer.name, customer_phone: customer.phone, deliverer_id: 0, status: 'pending' });

      // Reset form
      setAddress({
        address: '',
        complement: '',
        neighborhood: '',
        city: '',
      });
      setOrderDetails({
        products_total: 0,
        delivery_fee: 0,
        payment_method: 'pix',
        notes: '',
      });
      
      // Print receipt
      printReceipt({
        customer: customer,
        address: addressData,
        orderDetails: {
          ...orderDetails,
          total: orderDetails.products_total + orderDetails.delivery_fee,
        },
      });
    } catch (error) {
      console.error('Error creating order:', error);
    } finally {
      setLoading(false);
    }
  };

  const printReceipt = (data: ReceiptData) => {
    const receiptWindow = window.open('', '', 'width=300,height=600');
    if (!receiptWindow) return;

    const style = `
      <style>
        body { font-family: monospace; width: 80mm; margin: 0; padding: 10px; }
        .header { text-align: center; margin-bottom: 20px; }
        .divider { border-top: 1px dashed #000; margin: 10px 0; }
        .total { font-size: 1.2em; font-weight: bold; }
      </style>
    `;

    const content = `
      <div class="header">
        <h2>COMPROVANTE DE ENTREGA</h2>
        <p>${new Date().toLocaleString()}</p>
      </div>
      <div class="customer">
        <p><strong>Cliente:</strong> ${data.customer.name}</p>
        <p><strong>Telefone:</strong> ${data.customer.phone}</p>
      </div>
      <div class="divider"></div>
      <div class="address">
        <p><strong>Endereço de Entrega:</strong></p>
        <p>${data.address.address}${data.address.complement ? ` - ${data.address.complement}` : ''}</p>
        <p>${data.address.neighborhood} - ${data.address.city}</p>
      </div>
      <div class="divider"></div>
      <div class="order">
        <p><strong>Valor Produtos:</strong> R$ ${data.orderDetails.products_total.toFixed(2)}</p>
        <p><strong>Taxa de Entrega:</strong> R$ ${data.orderDetails.delivery_fee.toFixed(2)}</p>
        <p><strong>Forma de Pagamento:</strong> ${data.orderDetails.payment_method.toUpperCase()}</p>
        ${data.orderDetails.notes ? `<p><strong>Observações:</strong> ${data.orderDetails.notes}</p>` : ''}
      </div>
      <div class="divider"></div>
      <div class="total">
        <p>TOTAL: R$ ${data.orderDetails.total.toFixed(2)}</p>
      </div>
    `;

    receiptWindow.document.write(`<html><head>${style}</head><body>${content}</body></html>`);
    receiptWindow.document.close();
    receiptWindow.print();
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              value={phone}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setPhone(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Digite o telefone do cliente"
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={loading}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <Search className="h-5 w-5" />
          </button>
        </div>
      </div>

      {(phone && !customer || customer) && (
        <div className="bg-white shadow-sm rounded-lg p-6">
          {/* Orientação quando não há cliente */}
          {!customer && (
            <div className="mb-4 space-y-2">
              <span className="text-sm text-gray-500">Cliente não encontrado. Cadastre ou adicione endereço.</span>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder="Nome do cliente"
                  value={newCustomer.name}
                  onChange={e => setNewCustomer({ ...newCustomer, name: e.target.value })}
                  className="px-2 py-1 border border-gray-300 rounded-md"
                />
                <button
                  type="button"
                  onClick={handleCreateCustomer}
                  disabled={!newCustomer.name || loading}
                  className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  Cadastrar Cliente
                </button>
                <button
                  type="button"
                  onClick={() => setAddingNew(true)}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Adicionar Endereço
                </button>
              </div>
            </div>
          )}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Dados do Cliente</h3>
              <div className="space-y-4">
                <input
                  type="text"
                  value={newCustomer.name}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                  placeholder="Nome do cliente"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md"
                />
                {!customer && (
                  <button
                    onClick={handleCreateCustomer}
                    disabled={loading || !newCustomer.name}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 inline mr-2" />
                    Criar Novo Cliente
                  </button>
                )}
              </div>
            </div>

            {customer && (
              <div>
                <h3 className="text-lg font-medium mb-4">Endereço de Entrega</h3>
                {customerAddresses.length > 0 && (
                  <div className="space-y-2 mb-2">
                    <h4 className="font-medium">Selecione o endereço</h4>
                    {customerAddresses.map(addr => (
                      <label key={addr.id} className="flex items-center space-x-2">
                        <input
                          type="radio"
                          checked={selectedAddress?.id === addr.id}
                          onChange={() => setSelectedAddress(addr)}
                          className="h-4 w-4"
                        />
                        <span>{addr.address}{addr.complement ? ` - ${addr.complement}` : ''} - {addr.neighborhood}, {addr.city}</span>
                      </label>
                    ))}
                  </div>
                )}
                <button
                  type="button"
                  onClick={() => setAddingNew(prev => !prev)}
                  className="mb-4 text-blue-600"
                >
                  {addingNew ? 'Cancelar Endereço' : 'Adicionar / Editar Endereço'}
                </button>
                {addingNew && (
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="Endereço (Rua, Avenida, número...)"
                      value={address.address}
                      onChange={e => setAddress({ ...address, address: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Complemento (Apartamento, bloco, casa...)"
                      value={address.complement}
                      onChange={e => setAddress({ ...address, complement: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Bairro"
                      value={address.neighborhood}
                      onChange={e => setAddress({ ...address, neighborhood: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="text"
                      placeholder="Cidade"
                      value={address.city}
                      onChange={e => setAddress({ ...address, city: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                )}
              </div>
            )}

            {customer && (
              <div>
                <h3 className="text-lg font-medium mb-4">Detalhes do Pedido</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Valor Produtos
                      </label>
                      <input
                        type="number"
                        value={orderDetails.products_total}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => setOrderDetails({ ...orderDetails, products_total: parseFloat(e.target.value) || 0 })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md"
                        step="0.01"
                        min="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Taxa de Entrega
                      </label>
                      <input
                        type="number"
                        value={orderDetails.delivery_fee}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => setOrderDetails({ ...orderDetails, delivery_fee: parseFloat(e.target.value) || 0 })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md"
                        step="0.01"
                        min="0"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Forma de Pagamento
                    </label>
                    <select
                      value={orderDetails.payment_method}
                      onChange={(e: ChangeEvent<HTMLSelectElement>) => setOrderDetails({ ...orderDetails, payment_method: e.target.value as 'pix' | 'card' | 'cash' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="pix">PIX</option>
                      <option value="card">Cartão</option>
                      <option value="cash">Dinheiro</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Observações
                    </label>
                    <textarea
                      value={orderDetails.notes}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setOrderDetails({ ...orderDetails, notes: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      rows={3}
                    />
                  </div>
                  <button
                    onClick={handleCreateOrder}
                    disabled={loading || (!selectedAddress && !address.address && !address.neighborhood && !address.city)}
                    className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  >
                    <Printer className="h-4 w-4 inline mr-2" />
                    Finalizar e Imprimir
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerSearch;