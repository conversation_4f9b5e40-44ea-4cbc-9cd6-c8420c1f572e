-- Delivery CRM Database Schema for Supabase
-- Run this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_order_at TIMESTAMP WITH TIME ZONE
);

-- Create addresses table
CREATE TABLE IF NOT EXISTS addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    street TEXT NOT NULL,
    number TEXT NOT NULL,
    complement TEXT,
    neighborhood TEXT NOT NULL,
    city TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create deliverers table
CREATE TABLE IF NOT EXISTS deliverers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    phone TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    default_paid BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id),
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    deliverer_id UUID REFERENCES deliverers(id),
    status TEXT DEFAULT 'generated' CHECK (status IN ('generated', 'sent', 'delivered', 'returned', 'cancelled')),
    address_id UUID REFERENCES addresses(id),
    address JSONB,
    products_total DECIMAL(10,2) DEFAULT 0,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    payment_method TEXT DEFAULT 'cash',
    payment_amount DECIMAL(10,2),
    change_amount DECIMAL(10,2),
    is_paid BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reactivation_logs table
CREATE TABLE IF NOT EXISTS reactivation_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    result TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create caixa_operations table (for cash management)
CREATE TABLE IF NOT EXISTS caixa_operations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type TEXT NOT NULL CHECK (type IN ('suprimento', 'sangria')),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT
);

-- Create caixa_counts table (for daily cash counting)
CREATE TABLE IF NOT EXISTS caixa_counts (
    date DATE PRIMARY KEY,
    notes JSONB NOT NULL DEFAULT '{}',
    coins JSONB NOT NULL DEFAULT '{}',
    total DECIMAL(10,2) NOT NULL DEFAULT 0,
    closed INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default payment methods
INSERT INTO payment_methods (name, code, default_paid, is_active) VALUES
    ('Dinheiro', 'cash', FALSE, TRUE),
    ('Cartão', 'card', TRUE, TRUE),
    ('PIX', 'pix', TRUE, TRUE),
    ('Venda do Site', 'venda_do_site', TRUE, TRUE)
ON CONFLICT (code) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_addresses_customer_id ON addresses(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_deliverer_id ON orders(deliverer_id);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_reactivation_logs_customer_id ON reactivation_logs(customer_id);
CREATE INDEX IF NOT EXISTS idx_caixa_operations_created_at ON caixa_operations(created_at);
CREATE INDEX IF NOT EXISTS idx_caixa_counts_date ON caixa_counts(date);

-- Enable Row Level Security (RLS)
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE deliverers ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactivation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE caixa_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE caixa_counts ENABLE ROW LEVEL SECURITY;

-- Create policies to allow all operations for now (you can restrict these later)
CREATE POLICY "Allow all operations on customers" ON customers FOR ALL USING (true);
CREATE POLICY "Allow all operations on addresses" ON addresses FOR ALL USING (true);
CREATE POLICY "Allow all operations on deliverers" ON deliverers FOR ALL USING (true);
CREATE POLICY "Allow all operations on payment_methods" ON payment_methods FOR ALL USING (true);
CREATE POLICY "Allow all operations on orders" ON orders FOR ALL USING (true);
CREATE POLICY "Allow all operations on reactivation_logs" ON reactivation_logs FOR ALL USING (true);
CREATE POLICY "Allow all operations on caixa_operations" ON caixa_operations FOR ALL USING (true);
CREATE POLICY "Allow all operations on caixa_counts" ON caixa_counts FOR ALL USING (true);
