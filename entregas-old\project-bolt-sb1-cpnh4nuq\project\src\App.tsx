import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import NovaEntrega from './components/NovaEntrega';
import OrderManagement from './components/OrderManagement';
import ReactivationSystem from './components/ReactivationSystem';
import Configuracoes from './components/Configuracoes';
import CaixaManagement from './components/CaixaManagement';

function App() {
  return (
    <Router>
      <Toaster position="top-right" />
      <Layout>
        <Routes>
          <Route path="/" element={<OrderManagement />} />
          <Route path="/nova-entrega" element={<NovaEntrega />} />
          <Route path="/reativacao" element={<ReactivationSystem />} />
          <Route path="/configuracoes" element={<Configuracoes />} />
          <Route path="/caixa" element={<CaixaManagement />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;



