import React, { useState, useEffect } from 'react';
import {
  fetchDeliverers,
  fetchPaymentMethods,
  fetchCustomerByPhone,
  createCustomer,
  fetchAddresses,
  createAddress,
  createOrder,
  fetchCustomerLastOrders,
  fetchCustomerOrdersCount,
  fetchCustomerLastDeliveryAddress,
  debugCustomerData,
  Deliverer,
  Customer,
  Address,
  Order,
  PaymentMethod
} from '../lib/api';
// Removido import de tipos de '../types'; tipos agora vêm de 'api.ts'
import ThermalReceipt from './ThermalReceipt';
import { Search, Plus } from 'lucide-react';
import Toast from './Toast';

export default function NovaEntrega() {
  const [toast, setToast] = useState<{ message: string; type?: 'info' | 'success' | 'error' } | null>(null);
  const showToast = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 4000);
  };
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [customerAddresses, setCustomerAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [customerLastOrders, setCustomerLastOrders] = useState<Order[]>([]);
  const [customerOrdersCount, setCustomerOrdersCount] = useState<number>(0);
  const [lastDeliveryAddressId, setLastDeliveryAddressId] = useState<string | null>(null);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  type NewAddress = Omit<Address, 'id' | 'customer_id' | 'created_at'>;
  const [newAddress, setNewAddress] = useState<NewAddress>({ address: '', complement: '', neighborhood: '', city: '' });
  const [orderDetails, setOrderDetails] = useState<{ products_total: number; delivery_fee: number; payment_method: string; deliverer_id: string }>({
    products_total: 0,
    delivery_fee: 0,
    payment_method: '',
    deliverer_id: '',
  });
  const [deliverers, setDeliverers] = useState<Deliverer[]>([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [changeAmount, setChangeAmount] = useState<number>(0);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('cash'); // Definir 'cash' como padrão
  const [isPaid, setIsPaid] = useState(false);

  useEffect(() => {
    (async () => setDeliverers(await fetchDeliverers()))();
    (async () => setPaymentMethods(await fetchPaymentMethods()))();
  }, []);

  const formatPhone = (value: string) => {
    // Remove tudo que não for número
    const numbers = value.replace(/\D/g, '');
    
    // Se não tiver números, retorna vazio
    if (!numbers) return '';
    
    // Se tem DDD (começa com números diferentes de 8 ou 9)
    const hasDDD = numbers.length > 8 && !/^[89]/.test(numbers);
    
    if (hasDDD) {
      // Formata com DDD
      if (numbers.length <= 2) {
        return `(${numbers}`;
      }
      if (numbers.length <= 7) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      }
      if (numbers.length <= 11) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
      }
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    } else {
      // Formata sem DDD
      if (numbers.length <= 4) {
        return numbers;
      }
      if (numbers.length <= 8) {
        return `${numbers.slice(0, 4)}-${numbers.slice(4)}`;
      }
      return `${numbers.slice(0, 5)}-${numbers.slice(5, 9)}`;
    }
  };

  const normalizePhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    
    // Se tem 8 ou 9 dígitos, adiciona DDD 19
    if (numbers.length === 8 || numbers.length === 9) {
      return `19${numbers}`;
    }
    
    // Se já tem DDD (10 ou 11 dígitos), retorna como está
    if (numbers.length === 10 || numbers.length === 11) {
      return numbers;
    }
    
    return numbers;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setPhone(formatted);
  };

  const searchCustomerByPhone = async () => {
    if (!phone.trim()) return;
    const normalizedPhone = normalizePhone(phone);
    if (normalizedPhone.length < 10) {
      showToast('Número de telefone inválido', 'error');
      return;
    }
    setSearching(true);

    // Limpar estados anteriores
    setCustomerLastOrders([]);
    setCustomerOrdersCount(0);
    setLastDeliveryAddressId(null);

    try {
      const existing: Customer | null = await fetchCustomerByPhone(normalizedPhone);
      if (existing) {
        setCustomer(existing);
        setName(existing.name);

        // Carregar endereços e histórico do cliente
        try {
          const [addresses, lastOrders, ordersCount, lastDeliveryAddr] = await Promise.all([
            fetchAddresses(existing.id),
            fetchCustomerLastOrders(existing.id, 3).catch((err) => {
              console.warn('Could not load customer last orders:', err);
              return [];
            }),
            fetchCustomerOrdersCount(existing.id).catch((err) => {
              console.warn('Could not count customer orders:', err);
              return 0;
            }),
            fetchCustomerLastDeliveryAddress(existing.id).catch((err) => {
              console.warn('Could not find last delivery address:', err);
              return null;
            })
          ]);

          setCustomerAddresses(addresses);
          setCustomerLastOrders(lastOrders);
          setCustomerOrdersCount(ordersCount);
          setLastDeliveryAddressId(lastDeliveryAddr);

          // Debug temporário
          console.log('Customer history loaded:', {
            customerId: existing.id,
            addressesCount: addresses.length,
            lastOrdersCount: lastOrders.length,
            ordersCount,
            lastDeliveryAddr
          });

          // Debug detalhado
          await debugCustomerData(existing.id);
        } catch (error) {
          console.error('Error loading customer data:', error);
          // Carregar apenas endereços se tudo falhar
          try {
            setCustomerAddresses(await fetchAddresses(existing.id));
          } catch (addressError) {
            console.error('Error loading customer addresses:', addressError);
            setCustomerAddresses([]);
          }
          setCustomerLastOrders([]);
          setCustomerOrdersCount(0);
          setLastDeliveryAddressId(null);
        }
        return;
      }
      // Se não existe, só cria se o nome estiver preenchido
      if (!name.trim()) {
        showToast('Cliente não encontrado. Informe o nome para cadastrar.', 'error');
        return;
      }
      const cust = await createCustomer({ name: name.trim(), phone: normalizedPhone });
      setCustomer(cust);
      setName(cust.name);
      setCustomerAddresses(await fetchAddresses(cust.id));
    } catch (error: unknown) {
      console.error('Error fetching/creating customer:', error);
      showToast('Erro ao buscar ou cadastrar cliente', 'error');
    } finally {
      setSearching(false);
    }
  };

  const handleAddNewAddress = async () => {
    if (!customer) return;

    // Validação dos campos obrigatórios
    if (!newAddress.address.trim()) {
      alert('Por favor, preencha o endereço');
      return;
    }
    if (!newAddress.neighborhood.trim()) {
      alert('Por favor, preencha o bairro');
      return;
    }
    if (!newAddress.city.trim()) {
      alert('Por favor, preencha a cidade');
      return;
    }

    try {
      console.log('Criando endereço:', { customer_id: customer.id, ...newAddress });
      const newAddr = await createAddress({ customer_id: customer.id, ...newAddress });
      setCustomerAddresses([...customerAddresses, newAddr]);
      setSelectedAddress(newAddr);
      setShowNewAddressForm(false);
      setNewAddress({ address: '', complement: '', neighborhood: '', city: '' });
    } catch (error) {
      console.error('Error adding address:', error);
      alert(`Erro ao adicionar endereço: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedAddress || !name || !phone || !orderDetails.deliverer_id) {
      alert('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    const normalizedPhone = normalizePhone(phone);
    if (normalizedPhone.length < 10) {
      alert('Número de telefone inválido');
      return;
    }

    setLoading(true);
    let customerData: Customer;

    // Cadastro ou busca de cliente
    try {
      if (customer) {
        customerData =
          customer.name !== name.trim()
            ? await createCustomer({ name: name.trim(), phone: normalizedPhone })
            : customer;
      } else {
        const existing = await fetchCustomerByPhone(normalizedPhone);
        if (existing) {
          customerData =
            existing.name !== name.trim()
              ? await createCustomer({ name: name.trim(), phone: normalizedPhone })
              : existing;
        } else {
          customerData = await createCustomer({ name: name.trim(), phone: normalizedPhone });
        }
      }
    } catch (error: unknown) {
      console.error('Error creating customer:', error);
      if (error instanceof Error) {
        alert(`Erro ao criar cliente: ${error.message}`);
      } else {
        alert(`Erro ao criar cliente: ${String(error)}`);
      }
      setLoading(false);
      return;
    }

    // Criação de pedido
    try {
      const newOrder = await createOrder({
        customer_id: customerData.id,
        customer_name: customerData.name,
        customer_phone: normalizedPhone,
        deliverer_id: orderDetails.deliverer_id,
        status: 'generated',
        address_id: selectedAddress?.id,
        products_total: orderDetails.products_total,
        delivery_fee: orderDetails.delivery_fee,
        payment_method: selectedPaymentMethod,
        payment_amount: paymentAmount,
        change_amount: changeAmount,
        is_paid: isPaid,
      });

      // Buscar e anexar o endereço ao pedido para impressão
      const orderWithAddress = { ...newOrder, address: selectedAddress };
      setCurrentOrder(orderWithAddress);
      setShowReceipt(true);
    } catch (error: unknown) {
      console.error('Error creating order:', error);
      if (error instanceof Error) {
        alert(`Erro ao criar pedido: ${error.message}`);
      } else {
        alert(`Erro ao criar pedido: ${String(error)}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCloseReceipt = () => {
    setShowReceipt(false);
    setCurrentOrder(null);
    // Limpar formulário
    setPhone('');
    setName('');
    setCustomer(null);
    setCustomerAddresses([]);
    setSelectedAddress(null);
    setCustomerLastOrders([]);
    setCustomerOrdersCount(0);
    setLastDeliveryAddressId(null);
    setOrderDetails({
      products_total: 0,
      delivery_fee: 0,
      payment_method: '',
      deliverer_id: '',
    });
    // Resetar valores de pagamento
    setPaymentAmount(0);
    setChangeAmount(0);
    setSelectedPaymentMethod('cash');
    setIsPaid(false);
  };

  // Calcular troco quando o valor "troco para" mudar
  useEffect(() => {
    const total = orderDetails.products_total + orderDetails.delivery_fee;
    
    if (paymentAmount > 0) {
      setChangeAmount(Math.max(0, paymentAmount - total));
    } else {
      setChangeAmount(0);
    }
  }, [paymentAmount, orderDetails.products_total, orderDetails.delivery_fee]);

  const handlePaymentMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const method = e.target.value;
    console.log('Changing payment method to:', method);
    setSelectedPaymentMethod(method);
    
    // Se for venda do site, limpar os valores e desabilitar os campos
    if (method === 'venda_do_site') {
      setOrderDetails(prev => ({
        ...prev,
        payment_method: method,
        products_total: 0,
        delivery_fee: 0,
      }));
      setPaymentAmount(0);
      setChangeAmount(0);
      setIsPaid(true);
    }
  };

  // Se temos um pedido para mostrar o recibo
  if (showReceipt) {
    return (
      <ThermalReceipt
        order={currentOrder!}
        copies={2} // Imprimir 2 vias na criação do pedido
        onClose={() => {
          setShowReceipt(false);
          handleCloseReceipt();
        }}
      />
    );
  }

  return (
    <>
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
      <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-6">Nova Entrega</h2>

        {/* Busca por Telefone */}
        <div className="mb-6">
          <div className="max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Buscar Cliente por Telefone
            </label>
            <div className="flex space-x-2">
              <label htmlFor="customerPhone" className="sr-only">Telefone</label>
              <input
                id="customerPhone"
                name="customerPhone"
                type="tel"
                value={phone}
                onChange={handlePhoneChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    searchCustomerByPhone();
                  }
                }}
                placeholder="(19) 99999-9999"
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                required
                autoComplete="off"
              />
              <button
                type="button"
                onClick={searchCustomerByPhone}
                disabled={searching || !phone.trim()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {searching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Buscando...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Buscar
                  </>
                )}
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Digite o telefone do cliente para continuar com o pedido
            </p>
          </div>
        </div>

        {/* Dados do Cliente - Só aparecem após a busca */}
        {(customer || name) && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Dados do Cliente</h3>
            <div className="grid grid-cols-1 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome
                </label>
                <label htmlFor="customerName" className="sr-only">Nome</label>
                <input
                  id="customerName"
                  name="customerName"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                  autoComplete="off"
                />
              </div>
            </div>

            {/* Histórico do Cliente */}
            {customer && (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="text-sm font-medium text-blue-900 mb-3">
                  📊 Histórico do Cliente
                  {customerOrdersCount > 0 && ` (${customerOrdersCount} entregas finalizadas)`}
                </h4>

                {customerLastOrders.length > 0 ? (
                  <div className="space-y-2">
                    <p className="text-xs font-medium text-blue-800 mb-2">🕒 Últimas entregas:</p>
                    {customerLastOrders.map((order, index) => (
                      <div key={order.id} className="text-xs text-blue-700 bg-white p-2 rounded border">
                        <div className="flex justify-between items-start">
                          <div>
                            <span className="font-medium">
                              {new Date(order.created_at).toLocaleDateString('pt-BR')}
                            </span>
                            <span className={`ml-2 px-2 py-1 rounded text-xs ${
                              order.status === 'delivered'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {order.status === 'delivered' ? '✅ Entregue' : '↩️ Devolvido'}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              R$ {((order.products_total || 0) + (order.delivery_fee || 0)).toFixed(2).replace('.', ',')}
                            </div>
                            <div className="text-xs text-gray-500">
                              {order.payment_method === 'cash' ? '💵 Dinheiro' : '💳 Cartão'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-blue-600">
                    {customerOrdersCount === 0 ? '🆕 Cliente novo - nenhuma entrega finalizada ainda' : '📋 Carregando histórico...'}
                  </p>
                )}
              </div>
            )}

            {/* Endereços */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Endereço
                </label>
                {customer && (
                  <button
                    type="button"
                    onClick={() => setShowNewAddressForm(true)}
                    className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-700"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Novo Endereço
                  </button>
                )}
              </div>

          {showNewAddressForm ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label htmlFor="addressStreet" className="block text-sm font-medium text-gray-700 mb-1">
                    Endereço
                  </label>
                  <input
                    id="addressStreet"
                    name="addressStreet"
                    type="text"
                    placeholder="Rua, Avenida, número..."
                    value={newAddress.address}
                    onChange={(e) => setNewAddress({ ...newAddress, address: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    required
                    autoComplete="off"
                  />
                </div>
                <div>
                  <label htmlFor="addressComplement" className="block text-sm font-medium text-gray-700 mb-1">
                    Complemento
                  </label>
                  <input
                    id="addressComplement"
                    name="addressComplement"
                    type="text"
                    placeholder="Apartamento, bloco, casa..."
                    value={newAddress.complement || ''}
                    onChange={(e) => setNewAddress({ ...newAddress, complement: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    autoComplete="off"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="addressNeighborhood" className="block text-sm font-medium text-gray-700 mb-1">
                      Bairro
                    </label>
                    <input
                      id="addressNeighborhood"
                      name="addressNeighborhood"
                      type="text"
                      placeholder="Bairro"
                      value={newAddress.neighborhood}
                      onChange={(e) => setNewAddress({ ...newAddress, neighborhood: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      required
                      autoComplete="off"
                    />
                  </div>
                  <div>
                    <label htmlFor="addressCity" className="block text-sm font-medium text-gray-700 mb-1">
                      Cidade
                    </label>
                    <input
                      id="addressCity"
                      name="addressCity"
                      type="text"
                      placeholder="Cidade"
                      value={newAddress.city}
                      onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      required
                      autoComplete="off"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => setShowNewAddressForm(false)}
                  className="px-4 py-2 text-sm text-gray-700 hover:text-gray-900"
                >
                  Cancelar
                </button>
                <button
                  type="button"
                  onClick={handleAddNewAddress}
                  className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                >
                  Salvar Endereço
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {customerAddresses.map((addr) => (
                <label key={addr.id} className="flex items-center">
                  <input
                    type="radio"
                    checked={selectedAddress?.id === addr.id}
                    onChange={() => setSelectedAddress(addr)}
                    className="h-4 w-4 text-indigo-600 border-gray-300"
                    required
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {addr.address}{addr.complement ? `, ${addr.complement}` : ''} - {addr.neighborhood}, {addr.city}
                    {addr.is_default && (
                      <span className="ml-2 text-xs text-indigo-600">(Padrão)</span>
                    )}
                    {lastDeliveryAddressId === addr.id && (
                      <span className="ml-2 text-xs text-green-600 font-medium">(último realizado)</span>
                    )}
                  </span>
                </label>
              ))}
              {customerAddresses.length === 0 && !showNewAddressForm && (
                <p className="text-sm text-gray-500">
                  {customer
                    ? 'Nenhum endereço cadastrado. Adicione um novo endereço.'
                    : 'Busque um cliente pelo telefone ou adicione um novo endereço.'}
                </p>
              )}
            </div>
          )}
            </div>

            {/* Detalhes do Pedido */}
            <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Valores do Pedido</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor dos Produtos
                    {selectedPaymentMethod === 'venda_do_site' && (
                      <span className="ml-1 text-xs text-gray-500">(gerenciado pelo site)</span>
                    )}
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      type="number"
                      value={orderDetails.products_total || ''}
                      onChange={(e) => setOrderDetails({
                        ...orderDetails,
                        products_total: e.target.value ? parseFloat(e.target.value) : 0
                      })}
                      className={`
                        w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md
                        focus:ring-blue-500 focus:border-blue-500
                        ${selectedPaymentMethod === 'venda_do_site' ? 'bg-gray-50 cursor-not-allowed opacity-50' : ''}
                      `}
                      placeholder="0,00"
                      step="0.01"
                      disabled={selectedPaymentMethod === 'venda_do_site'}
                      required={selectedPaymentMethod !== 'venda_do_site'}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Taxa de Entrega
                    {selectedPaymentMethod === 'venda_do_site' && (
                      <span className="ml-1 text-xs text-gray-500">(gerenciado pelo site)</span>
                    )}
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      type="number"
                      value={orderDetails.delivery_fee || ''}
                      onChange={(e) => setOrderDetails({
                        ...orderDetails,
                        delivery_fee: e.target.value ? parseFloat(e.target.value) : 0
                      })}
                      className={`
                        w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md
                        focus:ring-blue-500 focus:border-blue-500
                        ${selectedPaymentMethod === 'venda_do_site' ? 'bg-gray-50 cursor-not-allowed opacity-50' : ''}
                      `}
                      placeholder="0,00"
                      step="0.01"
                      disabled={selectedPaymentMethod === 'venda_do_site'}
                      required={selectedPaymentMethod !== 'venda_do_site'}
                    />
                  </div>
                </div>
              </div>

              {/* Total do Pedido */}
              <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between items-center text-lg">
                  <span className="font-medium">Total do Pedido:</span>
                  <span className="font-bold text-blue-600">
                    R$ {((orderDetails.products_total || 0) + (orderDetails.delivery_fee || 0)).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            {/* Card de Pagamento */}
            <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Forma de Pagamento</h3>

              <div className="space-y-4">
                {/* Método de Pagamento */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Forma de Pagamento
                  </label>
                  <div className="space-y-2">
                    {paymentMethods.map((method) => (
                      <div key={method.code} className="flex items-center">
                        <input
                          type="radio"
                          id={`payment-${method.code}`}
                          name="paymentMethod"
                          value={method.code}
                          checked={selectedPaymentMethod === method.code}
                          onChange={handlePaymentMethodChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor={`payment-${method.code}`} className="ml-2 flex items-center gap-2">
                          <span className="text-gray-900">{method.name}</span>
                          {method.code === 'venda_do_site' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Valores automáticos • Pago
                            </span>
                          )}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {selectedPaymentMethod === 'venda_do_site' && (
                  <div className="rounded-md bg-blue-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-blue-800">Pedido do Site</h3>
                        <div className="mt-2 text-sm text-blue-700">
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Os valores são gerenciados automaticamente pelo sistema online</li>
                            <li>Não é necessário informar valores de produtos ou entrega</li>
                            <li>O pedido é considerado como já pago</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Campos de pagamento em dinheiro */}
                {selectedPaymentMethod === 'cash' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Troco Para
                      </label>
                      <div className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-gray-500 sm:text-sm">R$</span>
                        </div>
                        <input
                          type="number"
                          value={paymentAmount || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseFloat(e.target.value) : 0;
                            setPaymentAmount(value);
                          }}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder={`${(orderDetails.products_total + orderDetails.delivery_fee).toFixed(2)}`}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Troco na Sacolinha
                      </label>
                      <div className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-gray-500 sm:text-sm">R$</span>
                        </div>
                        <input
                          type="text"
                          value={changeAmount > 0 ? changeAmount.toFixed(2) : ''}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-gray-50"
                          placeholder="0,00"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Checkbox de pagamento - não mostrar para vendas do site */}
                {selectedPaymentMethod !== 'venda_do_site' && (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isPaid}
                      onChange={(e) => setIsPaid(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      Pedido já está pago
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* Entregador */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Entregador
              </label>
              <select
                value={orderDetails.deliverer_id}
                onChange={(e) => setOrderDetails({ ...orderDetails, deliverer_id: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                required
              >
                <option value="">Selecione um entregador</option>
                {deliverers.map((deliverer) => (
                  <option key={deliverer.id} value={deliverer.id}>
                    {deliverer.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Total */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total:</span>
                <span>R$ {(orderDetails.products_total + orderDetails.delivery_fee).toFixed(2)}</span>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="mt-6 w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {loading ? 'Criando pedido...' : 'Criar Pedido'}
            </button>
          </div>
        )}
      </div>
    </form>
    </>
  );
}
