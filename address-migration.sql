-- Migration script to update addresses table structure
-- Run this in your Supabase SQL Editor if you already have data

-- First, let's add the new address column
ALTER TABLE addresses ADD COLUMN IF NOT EXISTS address TEXT;

-- Update existing records to combine street and number into address
UPDATE addresses 
SET address = CONCAT(street, ', ', number) 
WHERE address IS NULL AND street IS NOT NULL AND number IS NOT NULL;

-- Update records where only street exists
UPDATE addresses 
SET address = street 
WHERE address IS NULL AND street IS NOT NULL AND (number IS NULL OR number = '');

-- Update records where only number exists (unlikely but just in case)
UPDATE addresses 
SET address = number 
WHERE address IS NULL AND number IS NOT NULL AND (street IS NULL OR street = '');

-- Make address column NOT NULL after data migration
ALTER TABLE addresses ALTER COLUMN address SET NOT NULL;

-- Drop the old columns (uncomment these lines after verifying data migration)
-- ALTER TABLE addresses DROP COLUMN IF EXISTS street;
-- ALTER TABLE addresses DROP COLUMN IF EXISTS number;

-- Note: If you haven't created any addresses yet, you can simply drop and recreate the table:
-- DROP TABLE IF EXISTS addresses;
-- Then run the updated schema from supabase-schema.sql
