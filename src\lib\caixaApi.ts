// API para operações de caixa (frontend)
export interface CaixaOperation {
  id: number;
  type: 'suprimento' | 'sangria';
  amount: number;
  description: string;
  created_at: string;
  created_by?: string;
}

export async function fetchCaixaOperations(): Promise<CaixaOperation[]> {
  const res = await fetch('/api/caixa');
  if (!res.ok) throw new Error('Erro ao buscar operações de caixa');
  return res.json();
}

export async function createCaixaOperation(data: Omit<CaixaOperation, 'id' | 'created_at'>): Promise<CaixaOperation> {
  const res = await fetch('/api/caixa', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!res.ok) throw new Error('Erro ao criar operação de caixa');
  return res.json();
}

export async function deleteCaixaOperation(id: number): Promise<void> {
  const res = await fetch(`/api/caixa/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error('Erro ao deletar operação');
}
