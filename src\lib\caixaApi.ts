// API para operações de caixa usando Supabase
import { supabase } from './supabase';

export interface CaixaOperation {
  id: string;
  type: 'suprimento' | 'sangria';
  amount: number;
  description: string;
  created_at: string;
  created_by?: string;
}

export async function fetchCaixaOperations(): Promise<CaixaOperation[]> {
  const { data, error } = await supabase
    .from('caixa_operations')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}

export async function createCaixaOperation(data: Omit<CaixaOperation, 'id' | 'created_at'>): Promise<CaixaOperation> {
  const { data: result, error } = await supabase
    .from('caixa_operations')
    .insert([data])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function deleteCaixaOperation(id: string): Promise<void> {
  const { error } = await supabase
    .from('caixa_operations')
    .delete()
    .eq('id', id);

  if (error) throw error;
}
