const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'database/local.db'));

const migrations = [
  // Adiciona colunas se não existirem
  `ALTER TABLE orders ADD COLUMN address_id INTEGER;`,
  `ALTER TABLE orders ADD COLUMN products_total REAL DEFAULT 0;`,
  `ALTER TABLE orders ADD COLUMN delivery_fee REAL DEFAULT 0;`,
  `ALTER TABLE orders ADD COLUMN payment_method TEXT;`,
  `ALTER TABLE orders ADD COLUMN payment_amount REAL;`,
  `ALTER TABLE orders ADD COLUMN change_amount REAL;`,
  `ALTER TABLE orders ADD COLUMN is_paid INTEGER DEFAULT 0;`
];

function runMigrations() {
  db.serialize(() => {
    let i = 0;
    function next() {
      if (i >= migrations.length) {
        console.log('Migração concluída.');
        db.close();
        return;
      }
      db.run(migrations[i], [], err => {
        if (err && !/duplicate|exists|already/.test(err.message)) {
          console.error('Erro na migração:', migrations[i], err.message);
        }
        i++;
        next();
      });
    }
    next();
  });
}

runMigrations();
