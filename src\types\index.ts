import React from 'react';

export interface Customer {
  id: string;
  phone: string;
  name: string;
  created_at: string;
  last_order_at: string | null;
}

export interface Address {
  id?: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  customer_id?: string;
  is_default?: boolean;
}

export interface Deliverer {
  id: string;
  name: string;
  is_active: boolean;
  created_at: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  code: string;
  default_paid: boolean;
  is_active: boolean;
  created_at: string;
}

export type OrderStatus = 'generated' | 'sent' | 'returned' | 'cancelled';

export interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_phone: string;
  address: Address;
  products_total: number;
  delivery_fee: number;
  total_amount?: number;
  payment_method: string;
  payment_method_name?: string;
  payment_amount?: number;
  change_amount?: number;
  is_paid: boolean;
  status: OrderStatus;
  deliverer_id: string;
  created_at: string;
}

export interface ReactivationLog {
  id: string;
  customer_id: string;
  contact_date: string;
  message: string;
  result: 'pending' | 'successful' | 'failed';
}

export interface InputEvent extends React.ChangeEvent<HTMLInputElement> {
  target: HTMLInputElement;
}

export interface TextAreaEvent extends React.ChangeEvent<HTMLTextAreaElement> {
  target: HTMLTextAreaElement;
}

export interface SelectEvent extends React.ChangeEvent<HTMLSelectElement> {
  target: HTMLSelectElement;
}

