// Script to remove customers with empty or null name from the SQLite database
const db = require('./database');

db.serialize(() => {
  db.run("DELETE FROM customers WHERE TRIM(name) = '' OR name IS NULL", function(err) {
    if (err) {
      console.error('Erro ao remover clientes sem nome:', err.message);
    } else {
      console.log(`Clientes removidos: ${this.changes}`);
    }
    db.close();
  });
});
