// API de operações de caixa para SQLite
const express = require('express');
const router = express.Router();
const db = require('./database');

// Listar operações
router.get('/', (req, res) => {
  db.all('SELECT * FROM caixa_operations ORDER BY created_at DESC', [], (err, rows) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json(rows);
  });
});

// Criar operação
router.post('/', (req, res) => {
  const { type, amount, description, created_by } = req.body;
  if (!['suprimento','sangria'].includes(type) || !amount) {
    return res.status(400).json({ error: 'Dados inválidos' });
  }
  db.run(
    'INSERT INTO caixa_operations (type, amount, description, created_by) VALUES (?, ?, ?, ?)',
    [type, amount, description || null, created_by || null],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      db.get('SELECT * FROM caixa_operations WHERE id = ?', [this.lastID], (err, row) => {
        if (err) return res.status(500).json({ error: err.message });
        res.json(row);
      });
    }
  );
});

// Deletar operação (opcional)
router.delete('/:id', (req, res) => {
  db.run('DELETE FROM caixa_operations WHERE id = ?', [req.params.id], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ success: true });
  });
});

module.exports = router;
