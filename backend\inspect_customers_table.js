const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'database/local.db'));

db.all("PRAGMA table_info(customers);", (err, rows) => {
  if (err) {
    console.error('Erro ao inspecionar tabela customers:', err.message);
  } else {
    console.log('Estrutura da tabela customers:');
    rows.forEach(col => console.log(col));
  }
  db.close();
});
