# Delivery CRM - Supabase Setup Guide

## Overview
This application now works **exclusively with Supabase**. All local backend files and SQLite dependencies have been removed.

## Step 1: Get Your Supabase Credentials

1. Go to https://supabase.com/dashboard
2. Select your project (reference: `hzszplndenimcoqmhinq`)
3. Go to **Settings** → **API**
4. Copy the following values:
   - **Project URL**: Should be `https://hzszplndenimcoqmhinq.supabase.co`
   - **anon public key**: Copy this key (it's a long JWT token)

## Step 2: Update Environment Variables

Update your `.env` file with the correct ANON_KEY:

```env
VITE_SUPABASE_URL=https://hzszplndenimcoqmhinq.supabase.co
VITE_SUPABASE_ANON_KEY=YOUR_ACTUAL_ANON_KEY_HERE
```

Replace `YOUR_ACTUAL_ANON_KEY_HERE` with the anon public key from Step 1.

## Step 3: Create Database Schema

1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Create a new query
4. Copy and paste the entire content from `supabase-schema.sql`
5. Click **Run** to execute the schema

This will create all the necessary tables:
- `customers`
- `addresses` (with separate street/number fields)
- `deliverers`
- `payment_methods`
- `orders`
- `reactivation_logs`
- `caixa_operations`
- `caixa_counts` (for daily cash counting)

## Step 4: Test the Connection

After completing steps 1-3, you can test your application:

1. Start your development server: `npm run dev`
2. Try creating a new deliverer in the Configurações page
3. Try creating a new order in Nova Entrega

## What's Been Updated

✅ **Removed Local Backend**: Completely removed `backend/` folder and all SQLite dependencies
✅ **API Layer**: All functions in `src/lib/api.ts`, `src/lib/caixaApi.ts`, and `src/lib/caixaCountApi.ts` now use Supabase exclusively
✅ **Supabase Client**: Proper client configuration in `src/lib/supabase.ts`
✅ **Environment Variables**: Updated to point to your Supabase project
✅ **Database Schema**: Complete schema with all tables including cash management
✅ **Package.json**: Cleaned up scripts, removed local backend references
✅ **Navigation**: Added cash management to main navigation

## Important Notes

- **ID Types**: Changed from `number` to `string` (UUIDs) throughout the application
- **Database**: PostgreSQL instead of SQLite
- **Authentication**: Currently using public access (you can add auth later)
- **Performance**: Added indexes for better query performance

## Troubleshooting

If you encounter issues:

1. **Check Environment Variables**: Make sure the ANON_KEY is correct
2. **Check Database Schema**: Ensure all tables were created successfully
3. **Check Browser Console**: Look for any API errors
4. **Check Supabase Logs**: Go to Logs section in Supabase dashboard

## Next Steps (Optional)

After basic functionality works:
- Add authentication
- Implement more restrictive RLS policies
- Add real-time subscriptions
- Optimize queries
