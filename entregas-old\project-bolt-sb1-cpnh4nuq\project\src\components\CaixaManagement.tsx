import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Printer, ArrowUp, ArrowDown, Calculator } from 'lucide-react';

// Tipos para as operações de caixa
interface CaixaOperation {
  id: string;
  type: 'suprimento' | 'sangria';
  amount: number;
  description: string;
  created_at: string;
  created_by?: string;
}

// Componente para recibo de operação
interface ReceiptProps {
  operation: CaixaOperation;
  onClose: () => void;
}

// Componente de recibo para impressão
function OperationReceipt({ operation, onClose }: ReceiptProps) {
  useEffect(() => {
    // Atraso pequeno para garantir que o DOM foi renderizado
    const timeoutId = setTimeout(() => {
      window.print();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []);

  const formatCurrency = (value: number) => {
    return `R$ ${(Math.round(value * 100) / 100).toFixed(2).replace('.', ',')}`;
  };

  const operationTypeText = operation.type === 'suprimento' ? 'SUPRIMENTO DE CAIXA' : 'SANGRIA DE CAIXA';

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-4">
          <div className="receipt">
            <style>
              {`
                @media print {
                  body * {
                    visibility: hidden;
                  }
                  .receipt, .receipt * {
                    visibility: visible;
                  }
                  .receipt {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 80mm;
                    padding: 5mm;
                  }
                  .no-print {
                    display: none;
                  }
                  /* Alto contraste para impressão térmica */
                  .receipt * {
                    color: black !important;
                    font-weight: bold !important;
                    text-shadow: none !important;
                  }
                  .receipt img {
                    filter: contrast(200%) brightness(0) !important;
                  }
                  .receipt-divider {
                    margin: 0.5em 0 !important;
                    border-top: 1px solid black !important;
                  }
                  .signature-line {
                    margin-top: 2em !important;
                    border-top: 1px solid black !important;
                    padding-top: 0.5em !important;
                  }
                }
                .receipt {
                  font-family: monospace;
                  width: 80mm;
                  margin: 0 auto;
                  padding: 5mm;
                  text-align: center;
                }
                .receipt-header {
                  text-align: center;
                  margin-bottom: 0.5em;
                }
                .receipt-logo {
                  height: 32px;
                  width: auto;
                  margin: 0 auto 0.5em;
                  display: block;
                  filter: contrast(200%) brightness(0);
                }
                .receipt-divider {
                  border-top: 1px solid black;
                  margin: 0.5em 0;
                }
                .receipt p {
                  margin: 0.3em 0;
                }
                .signature-line {
                  margin-top: 2em;
                  border-top: 1px solid black;
                  padding-top: 0.5em;
                  text-align: center;
                }
              `}
            </style>

            <div className="receipt-header">
              <img
                src="/recibo.png"
                alt="Delivery CRM"
                className="receipt-logo"
                onError={(e) => {
                  const img = e.target as HTMLImageElement;
                  img.onerror = null; // Previne loop infinito
                  img.src = '/receipt-logo.svg';
                }}
              />
              <div>
                <strong>{operationTypeText}</strong>
                <br />
                {new Date(operation.created_at).toLocaleString()}
              </div>
            </div>

            <div className="receipt-divider" />

            <div style={{ textAlign: 'center', margin: '1em 0' }}>
              <p><strong>Valor:</strong> {formatCurrency(operation.amount)}</p>
              <p><strong>Descrição:</strong> {operation.description}</p>
            </div>

            <div className="receipt-divider" />

            <div className="signature-line">
              Assinatura
            </div>
          </div>

          <div className="mt-4 flex justify-end no-print">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente principal de Gestão do Caixa
export default function CaixaManagement() {
  // Estados para operações
  const [operations, setOperations] = useState<CaixaOperation[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'operations' | 'count'>('operations');
  const [tableExists, setTableExists] = useState(true); // Estado para controlar se a tabela existe

  // Estados para nova operação
  const [operationType, setOperationType] = useState<'suprimento' | 'sangria'>('suprimento');
  const [amount, setAmount] = useState<string>('');
  const [description, setDescription] = useState<string>('');

  // Estado para recibo
  const [selectedOperation, setSelectedOperation] = useState<CaixaOperation | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);

  // Estados para contagem de caixa
  const [cashCount, setCashCount] = useState({
    notes: {
      '200': 0,
      '100': 0,
      '50': 0,
      '20': 0,
      '10': 0,
      '5': 0,
      '2': 0,
    },
    coins: {
      '1': 0,
      '0.5': 0,
      '0.25': 0,
      '0.1': 0,
      '0.05': 0,
      '0.01': 0,
    }
  });

  // Calcular total da contagem
  const calculateTotal = () => {
    let total = 0;

    // Somar notas
    Object.entries(cashCount.notes).forEach(([value, count]) => {
      total += parseFloat(value) * count;
    });

    // Somar moedas
    Object.entries(cashCount.coins).forEach(([value, count]) => {
      total += parseFloat(value) * count;
    });

    return total;
  };

  // Buscar operações do caixa
  const fetchOperations = async () => {
    setLoading(true);
    try {
      console.log('Buscando operações do caixa...');
      const { data, error } = await supabase
        .from('caixa_operations')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erro ao buscar operações:', error);

        if (error.code === '42P01') {
          // Tabela não existe
          console.log('Tabela não existe, usando armazenamento local');
          setTableExists(false);

          // Carregar operações do localStorage
          const savedOperations = localStorage.getItem('caixa_operations');
          if (savedOperations) {
            try {
              const parsedOperations = JSON.parse(savedOperations);
              console.log(`Carregadas ${parsedOperations.length} operações do localStorage`);
              setOperations(parsedOperations);
            } catch (parseError) {
              console.error('Erro ao parsear operações do localStorage:', parseError);
              setOperations([]);
            }
          } else {
            console.log('Nenhuma operação encontrada no localStorage');
            setOperations([]);
          }
        } else {
          throw error;
        }
      } else {
        console.log(`Carregadas ${data?.length || 0} operações do Supabase`);
        setTableExists(true);
        setOperations(data || []);
      }
    } catch (error) {
      console.error('Erro ao buscar operações:', error);
      // Não mostrar alerta, apenas usar armazenamento local
      const savedOperations = localStorage.getItem('caixa_operations');
      if (savedOperations) {
        try {
          const parsedOperations = JSON.parse(savedOperations);
          console.log(`Carregadas ${parsedOperations.length} operações do localStorage (fallback)`);
          setOperations(parsedOperations);
        } catch (parseError) {
          console.error('Erro ao parsear operações do localStorage:', parseError);
          setOperations([]);
        }
      } else {
        setOperations([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Carregar operações ao montar o componente
  useEffect(() => {
    fetchOperations();

    // Verificar se a tabela existe, se não, criar
    checkAndCreateTable();
  }, []);

  // Verificar se a tabela existe
  const checkAndCreateTable = async () => {
    try {
      console.log('Verificando se a tabela caixa_operations existe...');
      // Verificar se a tabela existe
      const { error } = await supabase
        .from('caixa_operations')
        .select('id')
        .limit(1);

      // Se der erro, provavelmente a tabela não existe
      if (error && error.code === '42P01') {
        console.log('Tabela caixa_operations não existe no banco de dados.');
        setTableExists(false);

        // Carregar operações do localStorage
        const savedOperations = localStorage.getItem('caixa_operations');
        if (savedOperations) {
          try {
            const parsedOperations = JSON.parse(savedOperations);
            console.log(`Encontradas ${parsedOperations.length} operações no localStorage`);
            setOperations(parsedOperations);
          } catch (parseError) {
            console.error('Erro ao parsear operações do localStorage:', parseError);
          }
        } else {
          console.log('Nenhuma operação encontrada no localStorage');
        }

        // Mostrar mensagem para o usuário criar a tabela
        console.log('\nVocê precisa criar a tabela caixa_operations no Supabase Studio.');
        console.log('URL: https://app.supabase.com/project/mdabanqlsirhqztaqbmc/sql');
      } else {
        console.log('Tabela caixa_operations existe no banco de dados!');
        setTableExists(true);
      }
    } catch (error) {
      console.error('Erro ao verificar tabela:', error);
      setTableExists(false);
    }
  };

  // Adicionar nova operação
  const handleAddOperation = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!amount || parseFloat(amount) <= 0) {
      alert('Informe um valor válido');
      return;
    }

    setLoading(true);
    try {
      // Preparar a nova operação (sem ID para o Supabase gerar automaticamente)
      const newOperation = {
        type: operationType,
        amount: parseFloat(amount),
        description: description.trim() || (operationType === 'suprimento' ? 'Suprimento de caixa' : 'Sangria de caixa'),
        created_at: new Date().toISOString(),
      };

      let savedOperation;

      if (tableExists) {
        // Tentar salvar no banco de dados
        const { data, error } = await supabase
          .from('caixa_operations')
          .insert(newOperation)
          .select()
          .single();

        if (error) {
          console.error('Erro ao inserir no Supabase:', error);

          if (error.code === '42P01') {
            // Tabela não existe, usar armazenamento local
            console.log('Tabela não existe, usando armazenamento local');
            setTableExists(false);

            // Adicionar ID para armazenamento local
            const localOperation = {
              ...newOperation,
              id: crypto.randomUUID()
            };

            savedOperation = localOperation;

            // Salvar no localStorage
            const updatedOperations = [localOperation, ...operations];
            localStorage.setItem('caixa_operations', JSON.stringify(updatedOperations));
          } else {
            throw error;
          }
        } else {
          console.log('Operação salva com sucesso no Supabase:', data);
          savedOperation = data;
        }
      } else {
        // Usar armazenamento local
        const localOperation = {
          ...newOperation,
          id: crypto.randomUUID()
        };

        savedOperation = localOperation;

        // Salvar no localStorage
        const updatedOperations = [localOperation, ...operations];
        localStorage.setItem('caixa_operations', JSON.stringify(updatedOperations));
      }

      // Atualizar lista de operações
      setOperations([savedOperation, ...operations]);

      // Limpar formulário
      setAmount('');
      setDescription('');

      // Mostrar recibo
      setSelectedOperation(savedOperation);
      setShowReceipt(true);

    } catch (error) {
      console.error('Erro ao adicionar operação:', error);
      alert('Não foi possível adicionar a operação');
    } finally {
      setLoading(false);
    }
  };

  // Imprimir recibo de uma operação existente
  const handlePrintReceipt = (operation: CaixaOperation) => {
    setSelectedOperation(operation);
    setShowReceipt(true);
  };

  // Salvar operações no localStorage quando mudar
  useEffect(() => {
    if (!tableExists && operations.length > 0) {
      localStorage.setItem('caixa_operations', JSON.stringify(operations));
    }
  }, [operations, tableExists]);

  // Atualizar contagem de notas
  const handleNoteCountChange = (value: string, count: number) => {
    setCashCount(prev => ({
      ...prev,
      notes: {
        ...prev.notes,
        [value]: count
      }
    }));
  };

  // Atualizar contagem de moedas
  const handleCoinCountChange = (value: string, count: number) => {
    setCashCount(prev => ({
      ...prev,
      coins: {
        ...prev.coins,
        [value]: count
      }
    }));
  };

  // Formatar valor para exibição
  const formatCurrency = (value: number) => {
    return `R$ ${value.toFixed(2).replace('.', ',')}`;
  };

  // Calcular saldo atual do caixa
  const calculateBalance = () => {
    let balance = 0;

    operations.forEach(op => {
      if (op.type === 'suprimento') {
        balance += op.amount;
      } else {
        balance -= op.amount;
      }
    });

    return balance;
  };

  // Mensagem de aviso sobre armazenamento local
  const renderStorageWarning = () => {
    if (!tableExists) {
      return (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                As operações estão sendo salvas localmente no seu navegador.
                Elas não serão compartilhadas com outros dispositivos.
              </p>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // Se estiver mostrando o recibo
  if (showReceipt && selectedOperation) {
    return (
      <OperationReceipt
        operation={selectedOperation}
        onClose={() => {
          setShowReceipt(false);
          setSelectedOperation(null);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-6">Gestão do Caixa</h2>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('operations')}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm
                ${activeTab === 'operations'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
              `}
            >
              Operações
            </button>
            <button
              onClick={() => setActiveTab('count')}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm
                ${activeTab === 'count'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
              `}
            >
              Contagem de Caixa
            </button>
          </nav>
        </div>

        {/* Conteúdo da tab de Operações */}
        {activeTab === 'operations' && (
          <div>
            {/* Aviso de armazenamento local */}
            {renderStorageWarning()}
            {/* Saldo atual */}
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="flex justify-between items-center">
                <span className="text-lg font-medium">Saldo Atual:</span>
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(calculateBalance())}
                </span>
              </div>
            </div>

            {/* Formulário para nova operação */}
            <form onSubmit={handleAddOperation} className="mb-8">
              <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Nova Operação</h3>

                {/* Tipo de operação */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Tipo de Operação
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={operationType === 'suprimento'}
                        onChange={() => setOperationType('suprimento')}
                        className="h-4 w-4 text-indigo-600 border-gray-300"
                      />
                      <span className="ml-2 flex items-center">
                        <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                        Suprimento (Entrada)
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={operationType === 'sangria'}
                        onChange={() => setOperationType('sangria')}
                        className="h-4 w-4 text-indigo-600 border-gray-300"
                      />
                      <span className="ml-2 flex items-center">
                        <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                        Sangria (Saída)
                      </span>
                    </label>
                  </div>
                </div>

                {/* Valor */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      type="number"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      step="0.01"
                      min="0.01"
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                      placeholder="0,00"
                      required
                    />
                  </div>
                </div>

                {/* Descrição */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição (opcional)
                  </label>
                  <input
                    type="text"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    placeholder={operationType === 'suprimento' ? "Suprimento de caixa" : "Sangria de caixa"}
                  />
                </div>

                {/* Botão de envio */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
                >
                  {loading ? 'Processando...' : 'Registrar Operação'}
                </button>
              </div>
            </form>

            {/* Lista de operações */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Histórico de Operações</h3>

              {loading ? (
                <p className="text-center py-4">Carregando...</p>
              ) : operations.length === 0 ? (
                <p className="text-center py-4 text-gray-500">Nenhuma operação registrada</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Data/Hora
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tipo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Valor
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Descrição
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ações
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {operations.map((operation) => (
                        <tr key={operation.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {format(new Date(operation.created_at), 'dd/MM/yyyy HH:mm')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              operation.type === 'suprimento'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {operation.type === 'suprimento' ? (
                                <>
                                  <ArrowUp className="h-3 w-3 mr-1" />
                                  Suprimento
                                </>
                              ) : (
                                <>
                                  <ArrowDown className="h-3 w-3 mr-1" />
                                  Sangria
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {formatCurrency(operation.amount)}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {operation.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handlePrintReceipt(operation)}
                              className="text-indigo-600 hover:text-indigo-900 flex items-center justify-end"
                            >
                              <Printer className="h-4 w-4 mr-1" />
                              Imprimir
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Conteúdo da tab de Contagem de Caixa */}
        {activeTab === 'count' && (
          <div>
            {/* Aviso de armazenamento local */}
            {renderStorageWarning()}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center mb-4">
                <Calculator className="h-5 w-5 text-indigo-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Contagem de Caixa</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Contagem de Notas */}
                <div>
                  <h4 className="text-md font-medium text-gray-700 mb-3">Notas</h4>
                  <div className="space-y-3">
                    {Object.entries(cashCount.notes).map(([value, count]) => (
                      <div key={`note-${value}`} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-16 font-medium text-gray-700">R$ {value}</div>
                          <span className="text-gray-500">×</span>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="number"
                            min="0"
                            value={count}
                            onChange={(e) => handleNoteCountChange(value, parseInt(e.target.value) || 0)}
                            className="w-20 px-3 py-1 border border-gray-300 rounded-md text-right"
                          />
                          <div className="w-24 text-right ml-3">
                            = {formatCurrency(parseFloat(value) * count)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Contagem de Moedas */}
                <div>
                  <h4 className="text-md font-medium text-gray-700 mb-3">Moedas</h4>
                  <div className="space-y-3">
                    {Object.entries(cashCount.coins).map(([value, count]) => (
                      <div key={`coin-${value}`} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-16 font-medium text-gray-700">
                            R$ {parseFloat(value).toFixed(2).replace('.', ',')}
                          </div>
                          <span className="text-gray-500">×</span>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="number"
                            min="0"
                            value={count}
                            onChange={(e) => handleCoinCountChange(value, parseInt(e.target.value) || 0)}
                            className="w-20 px-3 py-1 border border-gray-300 rounded-md text-right"
                          />
                          <div className="w-24 text-right ml-3">
                            = {formatCurrency(parseFloat(value) * count)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Total da Contagem */}
              <div className="mt-8 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium">Total em Caixa:</span>
                  <span className="text-xl font-bold text-blue-600">
                    {formatCurrency(calculateTotal())}
                  </span>
                </div>

                {/* Comparação com o saldo esperado */}
                {operations.length > 0 && (
                  <div className="mt-4 p-4 rounded-lg bg-gray-50">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Saldo Esperado:</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(calculateBalance())}
                      </span>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm font-medium">Diferença:</span>
                      <span className={`text-sm font-medium ${
                        calculateTotal() - calculateBalance() === 0
                          ? 'text-green-600'
                          : calculateTotal() - calculateBalance() > 0
                            ? 'text-blue-600'
                            : 'text-red-600'
                      }`}>
                        {formatCurrency(calculateTotal() - calculateBalance())}
                      </span>
                    </div>
                  </div>
                )}

                {/* Botão para imprimir relatório */}
                <button
                  onClick={() => window.print()}
                  className="mt-4 w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center justify-center"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Imprimir Relatório de Contagem
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
