import React, { useState } from 'react';
import { supabase } from '../lib/supabase';
import type { Customer, ReactivationLog } from '../types';
import { format } from 'date-fns';

export default function ReactivationSystem() {
  const [inactiveCustomers, setInactiveCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [inactiveDays, setInactiveDays] = useState(30);
  const [message, setMessage] = useState(
    'Olá! Sentimos sua falta! Que tal fazer um pedido hoje? Temos várias novidades esperando por você! 😊'
  );

  // Fetch inactive customers
  const fetchInactiveCustomers = async () => {
    setLoading(true);
    try {
      const date = new Date();
      date.setDate(date.getDate() - inactiveDays);

      const { data } = await supabase
        .from('customers')
        .select('*')
        .lt('last_order_at', date.toISOString())
        .order('last_order_at');
      
      setInactiveCustomers(data || []);
    } catch (error) {
      console.error('Error fetching inactive customers:', error);
    } finally {
      setLoading(false);
    }
  };

  // Generate WhatsApp link
  const generateWhatsAppLink = (phone: string) => {
    const formattedPhone = phone.replace(/\D/g, '');
    return `https://wa.me/${formattedPhone}?text=${encodeURIComponent(message)}`;
  };

  // Log reactivation attempt
  const logReactivationAttempt = async (customerId: string) => {
    try {
      await supabase.from('reactivation_logs').insert({
        customer_id: customerId,
        message,
        result: 'pending'
      });
    } catch (error) {
      console.error('Error logging reactivation attempt:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Sistema de Reativação</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Período de Inatividade (dias)
            </label>
            <input
              type="number"
              value={inactiveDays}
              onChange={(e) => setInactiveDays(parseInt(e.target.value))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Mensagem Personalizada
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>

          <button
            onClick={fetchInactiveCustomers}
            disabled={loading}
            className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? 'Buscando...' : 'Buscar Clientes Inativos'}
          </button>
        </div>

        {inactiveCustomers.length > 0 && (
          <div className="mt-6 space-y-4">
            <h3 className="text-lg font-medium">Clientes Inativos</h3>
            {inactiveCustomers.map((customer) => (
              <div key={customer.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-gray-500">{customer.phone}</p>
                    <p className="text-sm text-gray-500">
                      Último pedido: {customer.last_order_at ? format(new Date(customer.last_order_at), 'dd/MM/yyyy') : 'Nunca'}
                    </p>
                  </div>
                  <a
                    href={generateWhatsAppLink(customer.phone)}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => logReactivationAttempt(customer.id)}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    Enviar WhatsApp
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}