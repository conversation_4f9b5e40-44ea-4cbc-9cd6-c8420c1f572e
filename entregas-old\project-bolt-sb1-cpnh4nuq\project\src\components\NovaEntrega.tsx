import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Search, Plus } from 'lucide-react';
import type { Deliverer, Address, Customer, Order, PaymentMethod } from '../types';
import ThermalReceipt from './ThermalReceipt';

export default function NovaEntrega() {
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [customerAddresses, setCustomerAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [newAddress, setNewAddress] = useState<Address>({
    street: '',
    number: '',
    neighborhood: '',
    city: '',
  });
  const [orderDetails, setOrderDetails] = useState({
    products_total: 0,
    delivery_fee: 0,
    payment_method: 'cash', // Definir um valor padrão em vez de string vazia
    deliverer_id: '',
  });
  // Armazenar valores temporariamente quando mudar para venda do site
  const [savedValues, setSavedValues] = useState<{products_total: number, delivery_fee: number} | null>(null);
  const [deliverers, setDeliverers] = useState<Deliverer[]>([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [changeAmount, setChangeAmount] = useState<number>(0);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('cash'); // Definir 'cash' como padrão
  const [isPaid, setIsPaid] = useState(false);

  // Carregar entregadores quando o componente montar
  useEffect(() => {
    console.log('Componente montado - Estado inicial:', {
      phone,
      name,
      orderDetails,
      paymentAmount,
      changeAmount,
      selectedPaymentMethod
    });
    fetchDeliverers();
    fetchPaymentMethods();
  }, []);

  const fetchDeliverers = async () => {
    try {
      const { data } = await supabase
        .from('deliverers')
        .select('*')
        .eq('is_active', true)
        .order('name');
      setDeliverers(data || []);
    } catch (error) {
      console.error('Error fetching deliverers:', error);
    }
  };

  const fetchPaymentMethods = async () => {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('is_active', true) // Apenas métodos ativos
        .order('name');

      if (error) {
        console.error('Error fetching payment methods:', error);
        return;
      }

      if (!data || data.length === 0) {
        // Vamos tentar inserir alguns métodos de pagamento padrão
        const defaultMethods = [
          { name: 'Dinheiro', code: 'cash', default_paid: false, is_active: true },
          { name: 'Cartão', code: 'card', default_paid: true, is_active: true },
          { name: 'PIX', code: 'pix', default_paid: true, is_active: true },
          { name: 'Venda do Site', code: 'venda_do_site', default_paid: true, is_active: true }
        ];

        const { data: insertedData, error: insertError } = await supabase
          .from('payment_methods')
          .insert(defaultMethods)
          .select();

        if (insertError) {
          console.error('Error inserting default payment methods:', insertError);
          return;
        }

        setPaymentMethods(insertedData || []);

        if (insertedData?.length > 0) {
          const defaultMethod = insertedData.find(m => m.code === 'cash') || insertedData[0];
          setSelectedPaymentMethod(defaultMethod.code);
          setIsPaid(defaultMethod.default_paid);
        }
      } else {
        setPaymentMethods(data);

        const defaultMethod = data.find(m => m.code === 'cash') || data[0];
        setSelectedPaymentMethod(defaultMethod.code);
        setIsPaid(defaultMethod.default_paid);
      }
    } catch (error) {
      console.error('Unexpected error in fetchPaymentMethods:', error);
    }
  };

  // Atualiza o status de pagamento quando o método muda
  useEffect(() => {
    const method = paymentMethods.find(m => m.code === selectedPaymentMethod);
    if (method) {
      setIsPaid(method.default_paid);
      console.log('Payment method found:', method);
    }
  }, [selectedPaymentMethod, paymentMethods]);

  // Formatar telefone enquanto digita
  const formatPhone = (value: string) => {
    console.log('formatPhone - valor recebido:', value);
    // Remove tudo que não for número
    const numbers = value.replace(/\D/g, '');

    // Se não tiver números, retorna vazio
    if (!numbers) return '';

    // Se tem DDD (começa com números diferentes de 8 ou 9)
    const hasDDD = numbers.length > 8 && !/^[89]/.test(numbers);

    if (hasDDD) {
      // Formata com DDD
      if (numbers.length <= 2) {
        return `(${numbers}`;
      }
      if (numbers.length <= 7) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      }
      if (numbers.length <= 11) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
      }
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    } else {
      // Formata sem DDD
      if (numbers.length <= 4) {
        return numbers;
      }
      if (numbers.length <= 8) {
        return `${numbers.slice(0, 4)}-${numbers.slice(4)}`;
      }
      return `${numbers.slice(0, 5)}-${numbers.slice(5, 9)}`;
    }
  };

  // Normaliza telefone para busca/salvamento
  const normalizePhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');

    // Se tem 8 ou 9 dígitos, adiciona DDD 19
    if (numbers.length === 8 || numbers.length === 9) {
      return `19${numbers}`;
    }

    // Se já tem DDD (10 ou 11 dígitos), retorna como está
    if (numbers.length === 10 || numbers.length === 11) {
      return numbers;
    }

    return numbers;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handlePhoneChange - valor do input:', e.target.value);
    const formatted = formatPhone(e.target.value);
    console.log('handlePhoneChange - valor formatado:', formatted);
    setPhone(formatted);
  };

  const searchCustomerByPhone = async () => {
    if (!phone.trim()) return;

    const normalizedPhone = normalizePhone(phone);
    if (normalizedPhone.length < 10) {
      alert('Número de telefone inválido');
      return;
    }

    console.log('Antes da busca - Estado dos inputs:', {
      phone,
      name,
      orderDetails,
      paymentAmount,
      changeAmount,
      selectedPaymentMethod
    });

    setSearching(true);
    try {
      // Buscar cliente
      const { data: customerData, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('phone', normalizedPhone)
        .single();

      if (customerError && customerError.code !== 'PGRST116') {
        throw customerError;
      }

      if (customerData) {
        console.log('Cliente encontrado:', customerData);
        setCustomer(customerData);
        setName(customerData.name);

        // Buscar último pedido do cliente
        const { data: lastOrders } = await supabase
          .from('orders')
          .select('*')
          .eq('customer_id', customerData.id)
          .eq('status', 'delivered')
          .order('created_at', { ascending: false })
          .limit(1);

        console.log('Últimos pedidos:', lastOrders);

        // Buscar endereços do cliente
        const { data: addressesData } = await supabase
          .from('addresses')
          .select('*')
          .eq('customer_id', customerData.id);

        console.log('Endereços encontrados:', addressesData);

        if (addressesData) {
          setCustomerAddresses(addressesData);

          // Primeiro, procurar pelo endereço marcado como último utilizado (is_default = true)
          const defaultAddress = addressesData.find(addr => addr.is_default);
          if (defaultAddress) {
            // Se encontrou um endereço marcado como último utilizado, seleciona ele
            console.log('Selecionando endereço padrão:', defaultAddress);
            setSelectedAddress(defaultAddress);
          } else if (lastOrders && lastOrders.length > 0) {
            // Se não tem endereço marcado como último utilizado, mas tem último pedido
            const lastOrder = lastOrders[0];
            const lastUsedAddress = addressesData.find(addr =>
              addr.street === lastOrder.address.street &&
              addr.number === lastOrder.address.number &&
              addr.neighborhood === lastOrder.address.neighborhood &&
              addr.city === lastOrder.address.city
            );
            if (lastUsedAddress) {
              console.log('Selecionando endereço do último pedido:', lastUsedAddress);
              setSelectedAddress(lastUsedAddress);
            } else if (addressesData.length > 0) {
              // Se não encontrou nenhum dos anteriores, seleciona o primeiro endereço
              console.log('Selecionando primeiro endereço:', addressesData[0]);
              setSelectedAddress(addressesData[0]);
            }
          } else if (addressesData.length > 0) {
            // Se não tem último pedido nem endereço padrão, seleciona o primeiro endereço
            console.log('Selecionando primeiro endereço (sem pedidos):', addressesData[0]);
            setSelectedAddress(addressesData[0]);
          }
        }
      } else {
        console.log('Cliente não encontrado, limpando dados');
        setCustomer(null);
        setCustomerAddresses([]);
        setSelectedAddress(null);
      }
    } catch (error) {
      console.error('Error searching customer:', error);
      alert('Erro ao buscar cliente');
    } finally {
      setSearching(false);

      // Verificar estado após a busca
      console.log('Após a busca - Estado dos inputs:', {
        phone,
        name,
        orderDetails,
        paymentAmount,
        changeAmount,
        selectedPaymentMethod
      });
    }
  };

  const handleAddNewAddress = async () => {
    if (!customer) return;

    try {
      // Primeiro, definir todos os endereços do cliente como não padrão
      if (customerAddresses.length > 0) {
        await supabase
          .from('addresses')
          .update({ is_default: false })
          .eq('customer_id', customer.id);
      }

      // Depois, inserir o novo endereço como padrão
      const { data, error } = await supabase
        .from('addresses')
        .insert({
          customer_id: customer.id,
          ...newAddress,
          is_default: true // Sempre definir o novo endereço como último utilizado
        })
        .select()
        .single();

      if (error) throw error;

      // Atualizar a lista de endereços e selecionar o novo endereço
      const updatedAddresses = customerAddresses.map(addr => ({
        ...addr,
        is_default: false // Garantir que todos os endereços antigos não são padrão
      }));

      setCustomerAddresses([...updatedAddresses, data]);
      setSelectedAddress(data);
      setShowNewAddressForm(false);
      setNewAddress({
        street: '',
        number: '',
        neighborhood: '',
        city: '',
      });
    } catch (error) {
      console.error('Error adding address:', error);
      alert('Erro ao adicionar endereço');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (loading) return;
    setLoading(true);

    try {
      // Verificar se temos um cliente selecionado
      if (!customer) {
        throw new Error('Selecione um cliente');
      }

      // Verificar se temos um endereço selecionado
      if (!selectedAddress && !showNewAddressForm) {
        throw new Error('Selecione um endereço de entrega');
      }

      // Verificar se temos um método de pagamento selecionado
      if (!selectedPaymentMethod) {
        throw new Error('Selecione um método de pagamento');
      }

      // Verificar se temos um entregador selecionado
      if (!orderDetails.deliverer_id) {
        throw new Error('Selecione um entregador');
      }

      // Calcular o valor total do pedido
      const totalOrderValue = orderDetails.products_total + orderDetails.delivery_fee;

      // Se for pagamento em dinheiro e o campo "Troco para" estiver vazio ou zero,
      // definir o valor do pagamento como o valor total do pedido
      let finalPaymentAmount = paymentAmount;
      if (selectedPaymentMethod === 'cash' && (!paymentAmount || paymentAmount === 0)) {
        finalPaymentAmount = totalOrderValue;
        setPaymentAmount(totalOrderValue); // Atualizar o estado para refletir o valor correto
      }

      // Normalizar o telefone
      const normalizedPhone = phone.replace(/\D/g, '');

      // Se estiver criando um novo endereço
      let customerData = customer;
      if (showNewAddressForm) {
        // Criar novo cliente
        const { data, error } = await supabase
          .from('customers')
          .insert({
            phone: normalizedPhone,
            name: name.trim(),
            created_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) throw error;

        customerData = data;
      }

      // Criar novo endereço
      if (showNewAddressForm) {
        // Primeiro, definir todos os endereços existentes do cliente como não padrão
        // (se for um cliente existente)
        await supabase
          .from('addresses')
          .update({ is_default: false })
          .eq('customer_id', customerData.id);

        // Depois, inserir o novo endereço como padrão
        const { data, error } = await supabase
          .from('addresses')
          .insert({
            customer_id: customerData.id,
            ...newAddress,
            is_default: true // Sempre definir como último utilizado
          })
          .select()
          .single();

        if (error) throw error;

        setSelectedAddress(data);
      }

      // Criar pedido
      console.log('Selected payment method:', selectedPaymentMethod);

      // Dados do pedido para debug
      console.log('customerData:', customerData);
      console.log('customerData.id:', customerData?.id);

      const orderPayload = {
        customer_id: customerData?.id || '',
        customer_name: name.trim(),
        customer_phone: normalizedPhone,
        address: selectedAddress || {},
        products_total: selectedPaymentMethod === 'venda_do_site' ? 0 : orderDetails.products_total,
        delivery_fee: selectedPaymentMethod === 'venda_do_site' ? 0 : orderDetails.delivery_fee,
        payment_method: selectedPaymentMethod,
        payment_amount: selectedPaymentMethod === 'cash' ? finalPaymentAmount : null,
        change_amount: selectedPaymentMethod === 'cash' ? Math.max(0, finalPaymentAmount - totalOrderValue) : null,
        is_paid: selectedPaymentMethod === 'venda_do_site' ? true : isPaid,
        status: 'generated',
        deliverer_id: orderDetails.deliverer_id
      };

      console.log('Dados do pedido a serem enviados:', orderPayload);

      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_id: customerData?.id || '',
          customer_name: name.trim(),
          customer_phone: normalizedPhone,
          address: selectedAddress || {},
          products_total: selectedPaymentMethod === 'venda_do_site' ? 0 : orderDetails.products_total,
          delivery_fee: selectedPaymentMethod === 'venda_do_site' ? 0 : orderDetails.delivery_fee,
          payment_method: selectedPaymentMethod,
          payment_amount: selectedPaymentMethod === 'cash' ? finalPaymentAmount : null,
          change_amount: selectedPaymentMethod === 'cash' ? Math.max(0, finalPaymentAmount - totalOrderValue) : null,
          is_paid: selectedPaymentMethod === 'venda_do_site' ? true : isPaid,
          status: 'generated',
          deliverer_id: orderDetails.deliverer_id
        })
        .select()
        .single();

      if (orderError) {
        console.error('Error creating order:', orderError);
        throw orderError;
      }

      // Atualizar o endereço usado como padrão
      if (selectedAddress && selectedAddress.id) {
        // Primeiro, definir todos os endereços do cliente como não padrão
        await supabase
          .from('addresses')
          .update({ is_default: false })
          .eq('customer_id', customerData.id);

        // Depois, definir o endereço selecionado como padrão
        await supabase
          .from('addresses')
          .update({ is_default: true })
          .eq('id', selectedAddress.id);
      }

      // Mostrar recibo
      setCurrentOrder(orderData);
      setShowReceipt(true);

    } catch (error) {
      console.error('Error creating order:', error);
      alert('Erro ao criar pedido');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseReceipt = () => {
    setShowReceipt(false);
    setCurrentOrder(null);
    // Limpar formulário
    setPhone('');
    setName('');
    setCustomer(null);
    setCustomerAddresses([]);
    setSelectedAddress(null);
    setOrderDetails({
      products_total: 0,
      delivery_fee: 0,
      payment_method: 'cash', // Definir um valor padrão em vez de string vazia
      deliverer_id: '',
    });
    // Resetar valores de pagamento
    setPaymentAmount(0);
    setChangeAmount(0);
    setSelectedPaymentMethod('cash');
    setIsPaid(false);
  };

  // Calcular troco quando o valor "troco para" mudar
  useEffect(() => {
    const total = orderDetails.products_total + orderDetails.delivery_fee;

    if (paymentAmount > 0) {
      setChangeAmount(Math.max(0, paymentAmount - total));
    } else {
      setChangeAmount(0);
    }
  }, [paymentAmount, orderDetails.products_total, orderDetails.delivery_fee]);

  const handlePaymentMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const method = e.target.value;
    console.log('Changing payment method to:', method);
    setSelectedPaymentMethod(method);

    // Se for venda do site, salvar os valores atuais e limpar os campos
    if (method === 'venda_do_site') {
      // Salvar os valores atuais para restaurar depois
      setSavedValues({
        products_total: orderDetails.products_total,
        delivery_fee: orderDetails.delivery_fee
      });

      // Definir os valores para zero (não undefined)
      setOrderDetails(prev => ({
        ...prev,
        payment_method: method,
        products_total: 0,
        delivery_fee: 0,
      }));
      setPaymentAmount(0);
      setChangeAmount(0);
      setIsPaid(true);
    } else {
      // Se estiver voltando de venda do site para outro método, restaurar os valores salvos
      if (selectedPaymentMethod === 'venda_do_site' && savedValues) {
        setOrderDetails(prev => ({
          ...prev,
          payment_method: method,
          products_total: savedValues.products_total,
          delivery_fee: savedValues.delivery_fee,
        }));
      } else {
        // Apenas atualizar o método de pagamento
        setOrderDetails(prev => ({
          ...prev,
          payment_method: method,
        }));
      }

      // Atualizar o estado de pagamento com base no método selecionado
      const paymentMethod = paymentMethods.find(p => p.code === method);
      setIsPaid(paymentMethod?.default_paid || false);
    }
  };

  // Se temos um pedido para mostrar o recibo
  if (showReceipt) {
    return (
      <ThermalReceipt
        order={currentOrder!}
        onClose={() => {
          setShowReceipt(false);
          handleCloseReceipt();
        }}
      />
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-white shadow-sm rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-6">Nova Entrega</h2>

        {/* Dados do Cliente */}
        <div className="grid grid-cols-1 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Telefone
            </label>
            <div className="flex space-x-2 max-w-[300px]">
              <input
                type="tel"
                value={phone || ''} /* Garantir que o valor nunca seja undefined */
                onChange={handlePhoneChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    searchCustomerByPhone();
                  }
                }}
                placeholder="(19) 99999-9999"
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md"
                required
              />
              <button
                type="button"
                onClick={() => {
                  console.log('Botão de busca clicado - valor do telefone:', phone);
                  searchCustomerByPhone();
                }}
                disabled={searching || !phone.trim()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
              >
                {searching ? (
                  'Buscando...'
                ) : (
                  <>
                    <Search className="h-4 w-4" />
                    <span className="sr-only">Buscar cliente</span>
                  </>
                )}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nome
            </label>
            <input
              type="text"
              value={name || ''} /* Garantir que o valor nunca seja undefined */
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md"
              required
            />
          </div>
        </div>

        {/* Endereços */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <label className="block text-sm font-medium text-gray-700">
              Endereço
            </label>
            {customer && (
              <button
                type="button"
                onClick={() => setShowNewAddressForm(true)}
                className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Novo Endereço
              </button>
            )}
          </div>

          {showNewAddressForm ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2">
                  <input
                    type="text"
                    placeholder="Rua"
                    value={newAddress.street || ''} /* Garantir que o valor nunca seja undefined */
                    onChange={(e) => setNewAddress({ ...newAddress, street: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Número"
                    value={newAddress.number || ''} /* Garantir que o valor nunca seja undefined */
                    onChange={(e) => setNewAddress({ ...newAddress, number: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Bairro"
                    value={newAddress.neighborhood || ''} /* Garantir que o valor nunca seja undefined */
                    onChange={(e) => setNewAddress({ ...newAddress, neighborhood: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div className="col-span-2">
                  <input
                    type="text"
                    placeholder="Cidade"
                    value={newAddress.city || ''} /* Garantir que o valor nunca seja undefined */
                    onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => setShowNewAddressForm(false)}
                  className="px-4 py-2 text-sm text-gray-700 hover:text-gray-900"
                >
                  Cancelar
                </button>
                <button
                  type="button"
                  onClick={handleAddNewAddress}
                  className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                >
                  Salvar Endereço
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {customerAddresses.map((addr) => (
                <label key={addr.id} className="flex items-center">
                  <input
                    type="radio"
                    checked={selectedAddress?.id === addr.id}
                    onChange={() => setSelectedAddress(addr)}
                    className="h-4 w-4 text-indigo-600 border-gray-300"
                    required
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {addr.street}, {addr.number} - {addr.neighborhood}, {addr.city}
                    {addr.is_default && (
                      <span className="ml-2 text-xs text-indigo-600">(Último utilizado)</span>
                    )}
                  </span>
                </label>
              ))}
              {customerAddresses.length === 0 && !showNewAddressForm && (
                <p className="text-sm text-gray-500">
                  {customer
                    ? 'Nenhum endereço cadastrado. Adicione um novo endereço.'
                    : 'Busque um cliente pelo telefone ou adicione um novo endereço.'}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Detalhes do Pedido */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Valores do Pedido</h3>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Valor dos Produtos
                {selectedPaymentMethod === 'venda_do_site' && (
                  <span className="ml-1 text-xs text-gray-500">(gerenciado pelo site)</span>
                )}
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <span className="text-gray-500 sm:text-sm">R$</span>
                </div>
                <input
                  type="number"
                  value={orderDetails.products_total === 0 ? '0' : orderDetails.products_total || ''}
                  onChange={(e) => {
                    const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                    setOrderDetails({
                      ...orderDetails,
                      products_total: value
                    });
                  }}
                  className={`
                    w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md
                    focus:ring-blue-500 focus:border-blue-500
                    ${selectedPaymentMethod === 'venda_do_site' ? 'bg-gray-50 cursor-not-allowed opacity-50' : ''}
                  `}
                  placeholder="0,00"
                  step="0.01"
                  disabled={selectedPaymentMethod === 'venda_do_site'}
                  required={selectedPaymentMethod !== 'venda_do_site'}
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Taxa de Entrega
                {selectedPaymentMethod === 'venda_do_site' && (
                  <span className="ml-1 text-xs text-gray-500">(gerenciado pelo site)</span>
                )}
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <span className="text-gray-500 sm:text-sm">R$</span>
                </div>
                <input
                  type="number"
                  value={orderDetails.delivery_fee === 0 ? '0' : orderDetails.delivery_fee || ''}
                  onChange={(e) => {
                    const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                    setOrderDetails({
                      ...orderDetails,
                      delivery_fee: value
                    });
                  }}
                  className={`
                    w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md
                    focus:ring-blue-500 focus:border-blue-500
                    ${selectedPaymentMethod === 'venda_do_site' ? 'bg-gray-50 cursor-not-allowed opacity-50' : ''}
                  `}
                  placeholder="0,00"
                  step="0.01"
                  disabled={selectedPaymentMethod === 'venda_do_site'}
                  required={selectedPaymentMethod !== 'venda_do_site'}
                />
              </div>
            </div>
          </div>

          {/* Total do Pedido */}
          <div className="mt-4 bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center text-lg">
              <span className="font-medium">Total do Pedido:</span>
              <span className="font-bold text-blue-600">
                R$ {((orderDetails.products_total || 0) + (orderDetails.delivery_fee || 0)).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Card de Pagamento */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Forma de Pagamento</h3>

          <div className="space-y-4">
            {/* Método de Pagamento */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Forma de Pagamento
              </label>
              <div className="space-y-2">
                {paymentMethods.map((method) => (
                  <div key={method.code} className="flex items-center">
                    <input
                      type="radio"
                      id={`payment-${method.code}`}
                      name="paymentMethod"
                      value={method.code}
                      checked={selectedPaymentMethod === method.code}
                      onChange={handlePaymentMethodChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor={`payment-${method.code}`} className="ml-2 flex items-center gap-2">
                      <span className="text-gray-900">{method.name}</span>
                      {method.code === 'venda_do_site' && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          Valores automáticos • Pago
                        </span>
                      )}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {selectedPaymentMethod === 'venda_do_site' && (
              <div className="rounded-md bg-blue-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-blue-800">Pedido do Site</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <ul className="list-disc pl-5 space-y-1">
                        <li>Os valores são gerenciados automaticamente pelo sistema online</li>
                        <li>Não é necessário informar valores de produtos ou entrega</li>
                        <li>O pedido é considerado como já pago</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Campos de pagamento em dinheiro */}
            {selectedPaymentMethod === 'cash' && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Troco Para
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      type="number"
                      value={paymentAmount === 0 ? '0' : paymentAmount || ''}
                      onChange={(e) => {
                        const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                        setPaymentAmount(value);
                      }}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder={`${(orderDetails.products_total + orderDetails.delivery_fee).toFixed(2)}`}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Troco na Sacolinha
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      type="text"
                      value={changeAmount > 0 ? changeAmount.toFixed(2) : '0.00'} /* Sempre usar um valor definido */
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-gray-50"
                      placeholder="0,00"
                      disabled
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Checkbox de pagamento - não mostrar para vendas do site */}
            {selectedPaymentMethod !== 'venda_do_site' && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={isPaid}
                  onChange={(e) => setIsPaid(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Pedido já está pago
                </label>
              </div>
            )}
          </div>
        </div>

        {/* Entregador */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Entregador
          </label>
          <select
            value={orderDetails.deliverer_id}
            onChange={(e) => setOrderDetails({ ...orderDetails, deliverer_id: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 rounded-md"
            required
          >
            <option value="">Selecione um entregador</option>
            {deliverers.map((deliverer) => (
              <option key={deliverer.id} value={deliverer.id}>
                {deliverer.name}
              </option>
            ))}
          </select>
        </div>

        {/* Total */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Total:</span>
            <span>R$ {(orderDetails.products_total + orderDetails.delivery_fee).toFixed(2)}</span>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className="mt-6 w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
        >
          {loading ? 'Criando pedido...' : 'Criar Pedido'}
        </button>
      </div>
    </form>
  );
}
