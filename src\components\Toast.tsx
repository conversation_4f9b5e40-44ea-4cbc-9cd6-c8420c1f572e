import React from 'react';

interface ToastProps {
  message: string;
  type?: 'info' | 'success' | 'error';
  onClose: () => void;
}

const Toast: React.FC<ToastProps> = ({ message, type = 'info', onClose }) => {
  return (
    <div
      className={`fixed bottom-6 right-6 z-50 px-4 py-3 rounded shadow-lg bg-white border-l-4 transition-all
        ${type === 'success' ? 'border-green-500' : type === 'error' ? 'border-red-500' : 'border-blue-500'}`}
      role="alert"
      style={{ minWidth: 240 }}
    >
      <div className="flex justify-between items-center">
        <span className={`text-sm ${type === 'error' ? 'text-red-700' : type === 'success' ? 'text-green-700' : 'text-blue-700'}`}>{message}</span>
        <button
          className="ml-4 text-lg font-bold focus:outline-none"
          onClick={onClose}
          aria-label="Fechar aviso"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default Toast;
