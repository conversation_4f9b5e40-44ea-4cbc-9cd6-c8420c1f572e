// API usando Supabase
import { supabase } from './supabase';

export type Deliverer = { id: string; name: string; phone?: string; is_active: boolean; created_at: string; };

export async function fetchDeliverers(): Promise<Deliverer[]> {
  const { data, error } = await supabase
    .from('deliverers')
    .select('*')
    .order('name');

  if (error) throw error;
  return data || [];
}

export async function createDeliverer(data: { name: string; phone?: string; }): Promise<Deliverer> {
  const { data: result, error } = await supabase
    .from('deliverers')
    .insert([{ name: data.name, phone: data.phone, is_active: true }])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function updateDeliverer(id: string, data: { name: string; phone?: string; is_active: boolean; }): Promise<Deliverer> {
  const { data: result, error } = await supabase
    .from('deliverers')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function deleteDeliverer(id: string): Promise<void> {
  const { error } = await supabase
    .from('deliverers')
    .delete()
    .eq('id', id);

  if (error) throw error;
}

// Orders
export type Order = {
  id: string;
  customer_id?: string;
  customer_name: string;
  customer_phone: string;
  deliverer_id: string;
  status: string;
  created_at: string;
  address_id?: string;
  address?: Address;
  products_total: number;
  delivery_fee: number;
  payment_method: string;
  payment_amount?: number | null;
  change_amount?: number | null;
  is_paid: boolean;
};

export async function createOrder(data: {
  customer_id?: string;
  customer_name: string;
  customer_phone: string;
  deliverer_id: string;
  status?: string;
  address_id?: string;
  address?: any;
  products_total?: number;
  delivery_fee?: number;
  payment_method?: string;
  payment_amount?: number | null;
  change_amount?: number | null;
  is_paid?: boolean;
}): Promise<Order> {
  const { data: result, error } = await supabase
    .from('orders')
    .insert([{
      customer_id: data.customer_id,
      customer_name: data.customer_name,
      customer_phone: data.customer_phone,
      deliverer_id: data.deliverer_id,
      status: data.status || 'generated',
      address_id: data.address_id,
      address: data.address,
      products_total: data.products_total || 0,
      delivery_fee: data.delivery_fee || 0,
      payment_method: data.payment_method || 'cash',
      payment_amount: data.payment_amount,
      change_amount: data.change_amount,
      is_paid: data.is_paid || false
    }])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function fetchOrders(): Promise<Order[]> {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}

export async function updateOrder(id: string, data: { status: string; deliverer_id?: string; }): Promise<Order> {
  const { data: result, error } = await supabase
    .from('orders')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return result;
}

export type PaymentMethod = { id: string; name: string; code: string; default_paid: boolean; is_active: boolean; created_at: string };

export async function fetchPaymentMethods(): Promise<PaymentMethod[]> {
  const { data, error } = await supabase
    .from('payment_methods')
    .select('*')
    .eq('is_active', true)
    .order('name');

  if (error) throw error;
  return data || [];
}

export async function createPaymentMethod(data: { name: string; code: string; default_paid: boolean }): Promise<PaymentMethod> {
  const { data: result, error } = await supabase
    .from('payment_methods')
    .insert([{ ...data, is_active: true }])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function updatePaymentMethod(id: string, data: { name: string; code: string; default_paid: boolean; is_active: boolean }): Promise<PaymentMethod> {
  const { data: result, error } = await supabase
    .from('payment_methods')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function deletePaymentMethod(id: string): Promise<void> {
  const { error } = await supabase
    .from('payment_methods')
    .delete()
    .eq('id', id);

  if (error) throw error;
}

export type OrderConstraint = { column_name: string; type: string; notnull: number; default_value: unknown; pk: number };
export async function fetchOrderConstraints(): Promise<OrderConstraint[]> {
  // This would need to be implemented as a Supabase RPC function
  // For now, return empty array
  return [];
}

export type Customer = { id: string; name: string; phone: string; created_at: string; last_order_at?: string };

export async function fetchCustomerByPhone(phone: string): Promise<Customer | null> {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('phone', phone)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
  return data || null;
}

export async function createCustomer(data: { name: string; phone: string; }): Promise<Customer> {
  // First check if customer already exists
  const existing = await fetchCustomerByPhone(data.phone);
  if (existing) return existing;

  const { data: result, error } = await supabase
    .from('customers')
    .insert([data])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export async function fetchAllCustomers(): Promise<Customer[]> {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}

export type Address = { id: string; customer_id: string; address: string; complement?: string; neighborhood: string; city: string; is_default?: boolean; created_at: string };

export async function fetchAddresses(customer_id: string): Promise<Address[]> {
  const { data, error } = await supabase
    .from('addresses')
    .select('*')
    .eq('customer_id', customer_id)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}

// Buscar últimas entregas finalizadas do cliente
export async function fetchCustomerLastOrders(customerId: string, limit: number = 3): Promise<Order[]> {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('customer_id', customerId)
    .or('status.eq.delivered,status.eq.returned') // Alternativa ao in()
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching customer last orders:', error);
    return [];
  }
  return data || [];
}

// Contar total de pedidos finalizados do cliente
export async function fetchCustomerOrdersCount(customerId: string): Promise<number> {
  const { count, error } = await supabase
    .from('orders')
    .select('id', { count: 'exact', head: true })
    .eq('customer_id', customerId)
    .or('status.eq.delivered,status.eq.returned'); // Alternativa ao in()

  if (error) {
    console.error('Error counting customer orders:', error);
    return 0;
  }
  return count || 0;
}

// Buscar endereço da última entrega finalizada
export async function fetchCustomerLastDeliveryAddress(customerId: string): Promise<string | null> {
  const { data, error } = await supabase
    .from('orders')
    .select('address_id')
    .eq('customer_id', customerId)
    .or('status.eq.delivered,status.eq.returned') // Alternativa ao in()
    .order('created_at', { ascending: false })
    .limit(1);

  if (error) {
    console.error('Error fetching last delivery address:', error);
    return null;
  }

  if (!data || data.length === 0) {
    // Não é erro, apenas não há entregas finalizadas
    return null;
  }

  return data[0].address_id;
}

// Função de debug para verificar dados no banco
export async function debugCustomerData(customerId: string) {
  console.log('=== DEBUG CUSTOMER DATA ===');

  // Verificar todos os pedidos do cliente
  const { data: allOrders, error: ordersError } = await supabase
    .from('orders')
    .select('*')
    .eq('customer_id', customerId);

  console.log('All orders for customer:', allOrders);
  console.log('Orders error:', ordersError);

  // Verificar pedidos finalizados
  const { data: finishedOrders, error: finishedError } = await supabase
    .from('orders')
    .select('*')
    .eq('customer_id', customerId)
    .or('status.eq.delivered,status.eq.returned');

  console.log('Finished orders:', finishedOrders);
  console.log('Finished orders error:', finishedError);

  console.log('=== END DEBUG ===');
}

export async function createAddress(data: { customer_id: string; address: string; complement?: string; neighborhood: string; city: string; }): Promise<Address> {
  const { data: result, error } = await supabase
    .from('addresses')
    .insert([{ ...data, is_default: false }])
    .select()
    .single();

  if (error) throw error;
  return result;
}

export type ReactivationLog = { id: string; customer_id: string; message: string; result: string; created_at: string };

export async function createReactivationLog(data: { customer_id: string; message: string; result: string; }): Promise<ReactivationLog> {
  const { data: result, error } = await supabase
    .from('reactivation_logs')
    .insert([data])
    .select()
    .single();

  if (error) throw error;
  return result;
}
