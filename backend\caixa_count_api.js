// API para contagem e fechamento de caixa por dia
const express = require('express');
const router = express.Router();
const db = require('./database');

// Salvar contagem de caixa
router.post('/', (req, res) => {
  const { date, notes, coins, total } = req.body;
  if (!date || !notes || !coins || typeof total !== 'number') {
    return res.status(400).json({ error: 'Dados incompletos' });
  }
  db.run(
    `INSERT INTO caixa_counts (date, notes, coins, total) VALUES (?, ?, ?, ?)
     ON CONFLICT(date) DO UPDATE SET notes=excluded.notes, coins=excluded.coins, total=excluded.total`,
    [date, JSON.stringify(notes), JSON.stringify(coins), total],
    function(err) {
      if (err) return res.status(500).json({ error: err.message });
      res.json({ success: true });
    }
  );
});

// Buscar contagem do dia
router.get('/:date', (req, res) => {
  db.get('SELECT * FROM caixa_counts WHERE date = ?', [req.params.date], (err, row) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json(row);
  });
});

// Fechar o dia
router.post('/close', (req, res) => {
  const { date } = req.body;
  if (!date) return res.status(400).json({ error: 'Data obrigatória' });
  db.run('UPDATE caixa_counts SET closed = 1 WHERE date = ?', [date], function(err) {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ success: true });
  });
});

module.exports = router;
