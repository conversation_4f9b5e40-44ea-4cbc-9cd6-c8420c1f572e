const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Instancia o banco de dados SQLite
const db = new sqlite3.Database(
  path.join(__dirname, 'database', 'local.db'),
  sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
  (err) => {
    if (err) console.error('Erro ao conectar no banco de dados:', err);
    else console.log('Conectado ao SQLite em', path.join(__dirname, 'database', 'local.db'));
  }
);

module.exports = db;
