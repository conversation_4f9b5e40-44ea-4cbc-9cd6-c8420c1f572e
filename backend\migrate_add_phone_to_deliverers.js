const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'database/local.db'));

db.run('ALTER TABLE deliverers ADD COLUMN phone TEXT;', function(err) {
  if (err && !/duplicate|exists|already/.test(err.message)) {
    console.error('Erro ao adicionar coluna phone:', err.message);
  } else {
    console.log('Coluna phone adicionada (ou já existia).');
  }
  db.close();
});
